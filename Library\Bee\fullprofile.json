{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1380, "tid": 356, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1380, "tid": 356, "ts": 1751450706176024, "dur": 655, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1380, "tid": 356, "ts": 1751450706179108, "dur": 988, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1380, "tid": 1, "ts": 1751450705592321, "dur": 5419, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1380, "tid": 1, "ts": 1751450705597746, "dur": 98480, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1380, "tid": 1, "ts": 1751450705696247, "dur": 123282, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1380, "tid": 356, "ts": 1751450706180102, "dur": 48, "ph": "X", "name": "", "args": {}}, {"pid": 1380, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705590549, "dur": 7985, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705598537, "dur": 569473, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705599229, "dur": 2740, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705601978, "dur": 1561, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603547, "dur": 269, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603822, "dur": 24, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603849, "dur": 36, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603892, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603898, "dur": 35, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603936, "dur": 2, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705603939, "dur": 130, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604076, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604080, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604134, "dur": 4, "ph": "X", "name": "ProcessMessages 2008", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604140, "dur": 19, "ph": "X", "name": "ReadAsync 2008", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604160, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604163, "dur": 34, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604200, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604203, "dur": 35, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604240, "dur": 2, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604244, "dur": 107, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604357, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604371, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604411, "dur": 3, "ph": "X", "name": "ProcessMessages 1836", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604415, "dur": 106, "ph": "X", "name": "ReadAsync 1836", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604528, "dur": 2, "ph": "X", "name": "ProcessMessages 23", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604531, "dur": 51, "ph": "X", "name": "ReadAsync 23", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604585, "dur": 3, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604589, "dur": 93, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604685, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604689, "dur": 40, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604735, "dur": 4, "ph": "X", "name": "ProcessMessages 1441", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604741, "dur": 67, "ph": "X", "name": "ReadAsync 1441", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604815, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604819, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604876, "dur": 4, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604882, "dur": 103, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705604995, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605036, "dur": 3, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605046, "dur": 97, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605149, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605153, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605189, "dur": 3, "ph": "X", "name": "ProcessMessages 1956", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605194, "dur": 124, "ph": "X", "name": "ReadAsync 1956", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605326, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605329, "dur": 36, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605367, "dur": 3, "ph": "X", "name": "ProcessMessages 1827", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605381, "dur": 98, "ph": "X", "name": "ReadAsync 1827", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605487, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605538, "dur": 5, "ph": "X", "name": "ProcessMessages 2193", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605544, "dur": 94, "ph": "X", "name": "ReadAsync 2193", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605645, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605649, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605734, "dur": 8, "ph": "X", "name": "ProcessMessages 1960", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605744, "dur": 18, "ph": "X", "name": "ReadAsync 1960", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605767, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605801, "dur": 3, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605806, "dur": 104, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605916, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605921, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605961, "dur": 3, "ph": "X", "name": "ProcessMessages 1950", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705605965, "dur": 125, "ph": "X", "name": "ReadAsync 1950", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606096, "dur": 2, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606100, "dur": 38, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606141, "dur": 3, "ph": "X", "name": "ProcessMessages 1503", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606145, "dur": 78, "ph": "X", "name": "ReadAsync 1503", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606233, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606268, "dur": 2, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606271, "dur": 71, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606348, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606366, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606369, "dur": 23, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606394, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606397, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606423, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606426, "dur": 17, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606446, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606449, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606470, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606473, "dur": 12, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606487, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606490, "dur": 12, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606503, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606506, "dur": 19, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606527, "dur": 4, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606534, "dur": 23, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606563, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606566, "dur": 18, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606586, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606589, "dur": 21, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606612, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606614, "dur": 27, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606643, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606646, "dur": 25, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606672, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606675, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606701, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606704, "dur": 22, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606728, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606731, "dur": 24, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606757, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606759, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606781, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606806, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606808, "dur": 19, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606830, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606832, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606857, "dur": 6, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606865, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606886, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606888, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606916, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606918, "dur": 19, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606939, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606942, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606966, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606969, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606992, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705606994, "dur": 26, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607023, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607026, "dur": 63, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607092, "dur": 2, "ph": "X", "name": "ProcessMessages 1477", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607096, "dur": 23, "ph": "X", "name": "ReadAsync 1477", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607121, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607124, "dur": 20, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607146, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607149, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607174, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607176, "dur": 15, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607193, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607195, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607220, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607222, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607243, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607246, "dur": 21, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607269, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607272, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607293, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607295, "dur": 20, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607317, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607320, "dur": 108, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607432, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607456, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607459, "dur": 18, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607479, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607482, "dur": 24, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607508, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607511, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607528, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607531, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607554, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607556, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607577, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607579, "dur": 19, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607600, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607603, "dur": 22, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607632, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607635, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607658, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607661, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607682, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607684, "dur": 23, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607709, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607712, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607737, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607739, "dur": 20, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607762, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607764, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607785, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607807, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607810, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607830, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607832, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607854, "dur": 5, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607862, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607879, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607882, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607901, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607904, "dur": 18, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607924, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607926, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607948, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607950, "dur": 17, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607969, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607971, "dur": 18, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607991, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705607993, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608016, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608018, "dur": 46, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608069, "dur": 2, "ph": "X", "name": "ProcessMessages 1190", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608073, "dur": 18, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608093, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608095, "dur": 23, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608121, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608124, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608149, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608151, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608175, "dur": 1, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608178, "dur": 20, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608200, "dur": 5, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608208, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608231, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608233, "dur": 28, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608263, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608266, "dur": 22, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608290, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608292, "dur": 21, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608315, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608318, "dur": 23, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608343, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608346, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608366, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608368, "dur": 27, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608398, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608400, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608424, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608427, "dur": 17, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608447, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608450, "dur": 23, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608475, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608477, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608500, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608502, "dur": 16, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608521, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608523, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608543, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608546, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608573, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608576, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608595, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608598, "dur": 23, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608623, "dur": 4, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608630, "dur": 22, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608655, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608657, "dur": 18, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608677, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608680, "dur": 19, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608701, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608703, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608729, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608735, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608759, "dur": 2, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608763, "dur": 19, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608784, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608786, "dur": 23, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608811, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608814, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608838, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608840, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608872, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608874, "dur": 26, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608903, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608906, "dur": 18, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608927, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608929, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608955, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608962, "dur": 28, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608992, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705608995, "dur": 26, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609024, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609027, "dur": 23, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609052, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609054, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609083, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609086, "dur": 27, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609115, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609117, "dur": 24, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609143, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609146, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609167, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609170, "dur": 21, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609199, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609202, "dur": 22, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609227, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609229, "dur": 15, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609246, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609249, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609271, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609273, "dur": 22, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609297, "dur": 5, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609305, "dur": 23, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609330, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609333, "dur": 21, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609357, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609359, "dur": 28, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609389, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609391, "dur": 26, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609420, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609423, "dur": 25, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609450, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609453, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609471, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609473, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609500, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609502, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609525, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609528, "dur": 28, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609558, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609560, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609585, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609587, "dur": 26, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609616, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609618, "dur": 26, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609646, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609649, "dur": 26, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609678, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609680, "dur": 14, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609697, "dur": 25, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609725, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609727, "dur": 22, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609751, "dur": 2, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609755, "dur": 22, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609779, "dur": 5, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609786, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609806, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609808, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609830, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609833, "dur": 26, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609881, "dur": 24, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705609907, "dur": 163, "ph": "X", "name": "ProcessMessages 1466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610074, "dur": 53, "ph": "X", "name": "ReadAsync 1466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610129, "dur": 6, "ph": "X", "name": "ProcessMessages 4593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610136, "dur": 19, "ph": "X", "name": "ReadAsync 4593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610161, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610164, "dur": 20, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610186, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610188, "dur": 32, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610223, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610225, "dur": 22, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610254, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610256, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610280, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610283, "dur": 15, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610302, "dur": 20, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610324, "dur": 5, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610332, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610351, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610354, "dur": 28, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610384, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610386, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610409, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610411, "dur": 20, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610433, "dur": 5, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610440, "dur": 17, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610460, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610462, "dur": 19, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610484, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610486, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610506, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610508, "dur": 15, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610526, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610528, "dur": 17, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610546, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610549, "dur": 31, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610582, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610584, "dur": 17, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610604, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610606, "dur": 21, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610630, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610632, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610661, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610664, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610684, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610686, "dur": 36, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610725, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610728, "dur": 17, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610746, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610749, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610773, "dur": 20, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610796, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610799, "dur": 18, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610819, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610821, "dur": 19, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610846, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610849, "dur": 20, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610872, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610874, "dur": 17, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610894, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610896, "dur": 19, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610919, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610942, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610945, "dur": 16, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610964, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610966, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610985, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705610987, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611012, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611014, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611031, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611034, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611057, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611059, "dur": 17, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611078, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611080, "dur": 21, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611103, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611110, "dur": 20, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611132, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611135, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611156, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611158, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611182, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611185, "dur": 14, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611201, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611203, "dur": 52, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611258, "dur": 2, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611261, "dur": 19, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611282, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611285, "dur": 27, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611314, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611317, "dur": 28, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611351, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611356, "dur": 31, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611391, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611394, "dur": 33, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611430, "dur": 2, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611433, "dur": 29, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611465, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611468, "dur": 23, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611493, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611496, "dur": 27, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611525, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611528, "dur": 28, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611558, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611561, "dur": 17, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611581, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611583, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611604, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611607, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611629, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611631, "dur": 29, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611663, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611666, "dur": 23, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611691, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611694, "dur": 12, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611708, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611710, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611735, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611738, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611762, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611765, "dur": 19, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611786, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611789, "dur": 15, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611806, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611809, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611829, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611831, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611856, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611858, "dur": 23, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611884, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611887, "dur": 14, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611903, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611905, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611932, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611934, "dur": 20, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611956, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611959, "dur": 18, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611979, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705611982, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612001, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612004, "dur": 14, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612020, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612022, "dur": 38, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612069, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612072, "dur": 25, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612103, "dur": 2, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612107, "dur": 37, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612146, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612149, "dur": 26, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612177, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612180, "dur": 26, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612209, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612212, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612232, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612235, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612262, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612265, "dur": 29, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612296, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612299, "dur": 26, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612327, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612329, "dur": 21, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612352, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612354, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612385, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612388, "dur": 22, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612412, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612415, "dur": 16, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612433, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612435, "dur": 18, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612456, "dur": 4, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612463, "dur": 24, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612490, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612492, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612518, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612520, "dur": 17, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612540, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612543, "dur": 98, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612644, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612648, "dur": 32, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612682, "dur": 3, "ph": "X", "name": "ProcessMessages 2497", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612686, "dur": 16, "ph": "X", "name": "ReadAsync 2497", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612704, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612706, "dur": 22, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612732, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612734, "dur": 19, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612755, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612757, "dur": 30, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612789, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612792, "dur": 21, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612815, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612818, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612837, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612840, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612883, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612886, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612909, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612911, "dur": 24, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612938, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612940, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612962, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612964, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612984, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705612986, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613012, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613015, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613035, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613038, "dur": 20, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613059, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613062, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613085, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613088, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613111, "dur": 5, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613118, "dur": 21, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613141, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613144, "dur": 26, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613173, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613175, "dur": 22, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613199, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613202, "dur": 20, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613224, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613230, "dur": 23, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613256, "dur": 1, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613259, "dur": 23, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613284, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613286, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613306, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613309, "dur": 19, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613330, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613333, "dur": 24, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613359, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613362, "dur": 18, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613381, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613384, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613409, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613416, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613444, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613447, "dur": 22, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613471, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613474, "dur": 26, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613502, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613505, "dur": 25, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613532, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613534, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613562, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613565, "dur": 26, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613593, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613596, "dur": 26, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613625, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613627, "dur": 20, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613649, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613652, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613672, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613675, "dur": 18, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613697, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613722, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613724, "dur": 28, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613755, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613757, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613784, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613788, "dur": 22, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613812, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613814, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613866, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613889, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613891, "dur": 16, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613910, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613912, "dur": 19, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613934, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613936, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613959, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613961, "dur": 23, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613987, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705613989, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614009, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614011, "dur": 19, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614032, "dur": 4, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614039, "dur": 41, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614084, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614113, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614116, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614137, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614139, "dur": 23, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614165, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614167, "dur": 24, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614194, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614196, "dur": 17, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614219, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614222, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614243, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614245, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614295, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614326, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614329, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614353, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614356, "dur": 48, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614408, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614437, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614439, "dur": 16, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614457, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614460, "dur": 56, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614520, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614551, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614554, "dur": 25, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614582, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614584, "dur": 42, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614630, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614655, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614657, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614676, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614678, "dur": 60, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614743, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614765, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614767, "dur": 22, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614792, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614794, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614812, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614814, "dur": 44, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614863, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614885, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614888, "dur": 25, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614915, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614917, "dur": 14, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614933, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614935, "dur": 43, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705614982, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615003, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615005, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615028, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615030, "dur": 56, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615091, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615113, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615115, "dur": 25, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615142, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615145, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615168, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615170, "dur": 37, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615212, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615232, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615235, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615255, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615259, "dur": 20, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615281, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615284, "dur": 43, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615331, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615353, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615356, "dur": 26, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615384, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615387, "dur": 48, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615439, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615458, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615461, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615493, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615496, "dur": 47, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615547, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615571, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615574, "dur": 18, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615594, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615597, "dur": 53, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615654, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615677, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615680, "dur": 18, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615700, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615703, "dur": 58, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615765, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615788, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615790, "dur": 16, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615808, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615810, "dur": 32, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615853, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615855, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615876, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615903, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615905, "dur": 14, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615920, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615923, "dur": 13, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615937, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705615939, "dur": 61, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616005, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616034, "dur": 1, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616037, "dur": 23, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616063, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616065, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616113, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616141, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616143, "dur": 23, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616169, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616172, "dur": 42, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616218, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616247, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616249, "dur": 24, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616275, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616278, "dur": 44, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616326, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616362, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616367, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616402, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616407, "dur": 33, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616446, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616478, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616481, "dur": 26, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616509, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616512, "dur": 40, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616556, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616587, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616589, "dur": 24, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616615, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616619, "dur": 38, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616661, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616686, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616689, "dur": 16, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616708, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616710, "dur": 25, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616738, "dur": 5, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616745, "dur": 22, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616770, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616772, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616797, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616799, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616815, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616817, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616873, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616895, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616898, "dur": 22, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616923, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616925, "dur": 50, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705616980, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617003, "dur": 3, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617008, "dur": 25, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617036, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617038, "dur": 29, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617069, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617073, "dur": 25, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617100, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617103, "dur": 17, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617121, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617124, "dur": 19, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617150, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617152, "dur": 40, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617197, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617224, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617227, "dur": 22, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617252, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617254, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617306, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617333, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617336, "dur": 22, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617360, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617362, "dur": 46, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617413, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617434, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617437, "dur": 20, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617459, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617462, "dur": 54, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617521, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617542, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617545, "dur": 30, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617578, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617581, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617601, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617604, "dur": 51, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617659, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617686, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617689, "dur": 20, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617711, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617719, "dur": 32, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617754, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617757, "dur": 19, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617778, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617780, "dur": 19, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617802, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617804, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617821, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617823, "dur": 48, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617876, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617898, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617901, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617919, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617921, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617944, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617946, "dur": 45, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705617996, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618018, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618020, "dur": 23, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618045, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618048, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618072, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618074, "dur": 21, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618097, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618100, "dur": 27, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618130, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618132, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618163, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618165, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618207, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618228, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618231, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618253, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618255, "dur": 10, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618269, "dur": 57, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618331, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618355, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618358, "dur": 40, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618406, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618409, "dur": 28, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618439, "dur": 2, "ph": "X", "name": "ProcessMessages 1476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618443, "dur": 21, "ph": "X", "name": "ReadAsync 1476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618466, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618468, "dur": 23, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618493, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618496, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618538, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618559, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618562, "dur": 24, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618587, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618590, "dur": 52, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618647, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618671, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618674, "dur": 23, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618699, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618701, "dur": 50, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618756, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618780, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618783, "dur": 21, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618806, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618823, "dur": 20, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618846, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618848, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618866, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618868, "dur": 31, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618901, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618903, "dur": 23, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618928, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618931, "dur": 18, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618951, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618954, "dur": 24, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618980, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705618983, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619008, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619010, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619066, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619098, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619101, "dur": 20, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619123, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619126, "dur": 43, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619173, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619200, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619202, "dur": 24, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619229, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619232, "dur": 46, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619283, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619311, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619314, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619335, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619337, "dur": 104, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619449, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619451, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619482, "dur": 2, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619485, "dur": 20, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619507, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619510, "dur": 20, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619532, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619534, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619557, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619559, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619587, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619590, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619612, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619615, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619639, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619642, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619697, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619721, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619724, "dur": 15, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619741, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619744, "dur": 58, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619806, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619844, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619847, "dur": 30, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619884, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619888, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619917, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619947, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619950, "dur": 17, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619969, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619972, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619996, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705619998, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620049, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620068, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620071, "dur": 15, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620092, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620095, "dur": 36, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620135, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620152, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620155, "dur": 47, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620206, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620237, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620240, "dur": 16, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620259, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620261, "dur": 51, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620317, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620343, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620346, "dur": 22, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620371, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620373, "dur": 16, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620391, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620394, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620439, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620461, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620463, "dur": 20, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620485, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620488, "dur": 55, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620548, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620575, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620578, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620596, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620599, "dur": 28, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620629, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620632, "dur": 29, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620663, "dur": 2, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620666, "dur": 21, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620689, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620691, "dur": 15, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620708, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620711, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620768, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620797, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620800, "dur": 23, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620825, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620828, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620877, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620902, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620904, "dur": 16, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620922, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620924, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620953, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705620955, "dur": 56, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621016, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621057, "dur": 2, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621060, "dur": 24, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621086, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621089, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621128, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621173, "dur": 2, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621177, "dur": 44, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621227, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621252, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621254, "dur": 23, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621279, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621282, "dur": 50, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621336, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621357, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621360, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621381, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621384, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621407, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621409, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621454, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621509, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621512, "dur": 16, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621530, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621532, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621567, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621597, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621600, "dur": 17, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621619, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621622, "dur": 49, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621675, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621697, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621699, "dur": 25, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621726, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621729, "dur": 14, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621745, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621747, "dur": 40, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621791, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621809, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621812, "dur": 24, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621838, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621840, "dur": 23, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621865, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621868, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621906, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621942, "dur": 2, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621946, "dur": 25, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621973, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705621975, "dur": 40, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622020, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622052, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622055, "dur": 18, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622075, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622077, "dur": 45, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622127, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622148, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622151, "dur": 28, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622181, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622183, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622207, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622209, "dur": 24, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622235, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622238, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622257, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622260, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622284, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622286, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622345, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622370, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622373, "dur": 21, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622397, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622399, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622448, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622469, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622471, "dur": 17, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622491, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622493, "dur": 21, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622516, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622518, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622564, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622594, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622597, "dur": 17, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622617, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622619, "dur": 53, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622676, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622704, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622707, "dur": 16, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622725, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622727, "dur": 56, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622787, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622808, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622810, "dur": 23, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622841, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622844, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622867, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622870, "dur": 26, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622898, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622901, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622918, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622920, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622944, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622946, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705622999, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623035, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623038, "dur": 15, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623055, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623057, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623107, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623130, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623132, "dur": 17, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623151, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623154, "dur": 57, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623216, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623238, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623241, "dur": 12, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623255, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623257, "dur": 58, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623320, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623354, "dur": 2, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623357, "dur": 14, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623373, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623375, "dur": 59, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623438, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623460, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623464, "dur": 26, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623492, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623495, "dur": 46, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623546, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623575, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623578, "dur": 16, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623596, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623598, "dur": 50, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623652, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623678, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623681, "dur": 23, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623706, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623709, "dur": 48, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623761, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623795, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623798, "dur": 24, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623824, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623827, "dur": 42, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623873, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623904, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623906, "dur": 28, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623937, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623940, "dur": 22, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623964, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623967, "dur": 26, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623996, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705623998, "dur": 25, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624026, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624028, "dur": 20, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624050, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624053, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624092, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624114, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624117, "dur": 27, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624146, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624149, "dur": 49, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624202, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624232, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624234, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624264, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624267, "dur": 21, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624290, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624293, "dur": 36, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624331, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624334, "dur": 23, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624360, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624362, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624407, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624431, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624434, "dur": 27, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624463, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624466, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624487, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624490, "dur": 25, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624517, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624519, "dur": 27, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624549, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624552, "dur": 15, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624569, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624571, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624617, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624645, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624647, "dur": 23, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624673, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624676, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624698, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624701, "dur": 24, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624727, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624729, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624755, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624757, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624774, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624776, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624826, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624853, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624855, "dur": 40, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624900, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705624921, "dur": 321, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625247, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625294, "dur": 9, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625305, "dur": 40, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625352, "dur": 7, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625362, "dur": 38, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625406, "dur": 5, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625414, "dur": 38, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625457, "dur": 3, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625463, "dur": 29, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625496, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625501, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625535, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625540, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625576, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625584, "dur": 33, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625621, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625627, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625657, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625660, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625695, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625701, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625739, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625744, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625776, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625781, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625806, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625811, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625833, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625837, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625868, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625872, "dur": 25, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625900, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625904, "dur": 22, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625929, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625934, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625991, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705625998, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626047, "dur": 7, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626056, "dur": 39, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626099, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626105, "dur": 41, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626151, "dur": 4, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626157, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626187, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626193, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626229, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626236, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626271, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626277, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626303, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626309, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626342, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626346, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626377, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626384, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626417, "dur": 4, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626424, "dur": 32, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626460, "dur": 4, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626466, "dur": 31, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626502, "dur": 4, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626509, "dur": 28, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626542, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626549, "dur": 47, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626600, "dur": 7, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626610, "dur": 34, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626648, "dur": 4, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626655, "dur": 33, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626693, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626700, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626751, "dur": 4, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626757, "dur": 33, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626792, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626796, "dur": 93, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626897, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626943, "dur": 5, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626950, "dur": 29, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626984, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705626993, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627034, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627044, "dur": 33, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627081, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627087, "dur": 21, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627111, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627117, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627147, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627153, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627207, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627228, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627271, "dur": 8, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627282, "dur": 39, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627327, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705627335, "dur": 5129, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705632472, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705632478, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705632505, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705632509, "dur": 319, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705632832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705632836, "dur": 219, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705633061, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705633072, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705633105, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705633111, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705633136, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705633140, "dur": 1935, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635079, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635084, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635107, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635290, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635294, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635322, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635324, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635417, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635453, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635456, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635787, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635821, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635825, "dur": 162, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635992, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705635995, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636019, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636023, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636042, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636045, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636065, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636069, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636204, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636209, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636245, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636249, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636432, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636448, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636452, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636465, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636468, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636605, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636609, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636629, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636633, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636648, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636651, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636665, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636668, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636725, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636743, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636746, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636782, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636802, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636806, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636826, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636831, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636848, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636852, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636889, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636908, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636911, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636928, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636931, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705636989, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637002, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637005, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637020, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637024, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637084, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637103, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637106, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637133, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637150, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637153, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637225, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637268, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637278, "dur": 94, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637382, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637405, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637410, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637434, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637439, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637459, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637462, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637591, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637595, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637614, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637619, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637657, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637674, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637678, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637912, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637915, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637932, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637935, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705637998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638001, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638022, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638026, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638044, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638047, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638064, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638067, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638163, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638182, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638186, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638200, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638203, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638225, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638229, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638262, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638266, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638297, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638302, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638330, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638334, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638443, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638460, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638464, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638503, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638520, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638525, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638562, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638565, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638582, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638586, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638657, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638673, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638676, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638696, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638699, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638741, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638744, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638758, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638762, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638792, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638796, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638814, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638818, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638866, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638882, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638885, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638957, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638960, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638976, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705638980, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639049, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639064, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639067, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639081, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639084, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639122, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639138, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639141, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639162, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639167, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639190, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639194, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639214, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639217, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639263, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639268, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639285, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639288, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639337, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639344, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639368, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639372, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639423, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639428, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639452, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639456, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639471, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639474, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639487, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639490, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639564, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639567, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639586, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639590, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639611, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639615, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639632, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639636, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639674, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639680, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639713, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639717, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639759, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639790, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639794, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639811, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639814, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639837, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639840, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639869, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639874, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639893, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639896, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639917, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639920, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639947, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705639951, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640070, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640073, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640166, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640170, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640210, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640213, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640230, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640232, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640260, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640263, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640284, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640304, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640306, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640474, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640478, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640501, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640506, "dur": 110, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640621, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640625, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640654, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640657, "dur": 92, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640756, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640850, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640855, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640882, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705640886, "dur": 262, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641152, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641155, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641174, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641178, "dur": 87, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641271, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641276, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641309, "dur": 601, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641914, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641957, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641961, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641984, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705641986, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642012, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642017, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642159, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642163, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642262, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642269, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642306, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642311, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642381, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642402, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642406, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642433, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642436, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642573, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642593, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642595, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642697, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642714, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642718, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642864, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642899, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642902, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642930, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705642932, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643014, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643018, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643038, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643042, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643063, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643067, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643082, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643085, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643108, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643110, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643126, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643282, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643285, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643306, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643310, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643408, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643411, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643449, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643452, "dur": 103, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643561, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643564, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643587, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643589, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643623, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643648, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643650, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643667, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643669, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643835, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643861, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643864, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643923, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643938, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705643940, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644053, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644079, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644097, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644099, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644258, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644274, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644276, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644323, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644338, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644341, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644379, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644408, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644411, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644437, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644439, "dur": 421, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644865, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644868, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644900, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644904, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644957, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644977, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705644979, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645016, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645032, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645034, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645239, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645266, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645268, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645338, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645365, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645367, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645402, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645426, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645428, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645460, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645488, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645490, "dur": 246, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645741, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645771, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705645774, "dur": 654, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705646436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705646441, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705646473, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705646478, "dur": 28367, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705674856, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705674861, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705674919, "dur": 1482, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705676408, "dur": 6519, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705682937, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705682943, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705682976, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705682982, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683001, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683005, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683021, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683024, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683106, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683111, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683134, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683137, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683390, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683411, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683414, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683429, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683433, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683451, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683454, "dur": 526, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705683987, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684005, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684007, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684142, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684147, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684173, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684177, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684259, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684263, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684288, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684292, "dur": 467, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684768, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684791, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705684795, "dur": 519, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685321, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685352, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685357, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685472, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685493, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685496, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685541, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685544, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685560, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685563, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685591, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685610, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705685613, "dur": 390, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686012, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686032, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686035, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686137, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686141, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686163, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686166, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686308, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686323, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686326, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686360, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686363, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686383, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686386, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686431, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686460, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686466, "dur": 315, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686786, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686790, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705686814, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687051, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687081, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687085, "dur": 605, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687697, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687724, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687836, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687838, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687876, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687881, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687941, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687969, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705687975, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688002, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688005, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688218, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688222, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688247, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688250, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688400, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688422, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688425, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688664, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688667, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688686, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688688, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688746, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688767, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688770, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688925, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688947, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688950, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705688989, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689010, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689013, "dur": 412, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689432, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689455, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689458, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689497, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689517, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689521, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689544, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689546, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689570, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705689574, "dur": 967, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690548, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690574, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690578, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690626, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690645, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690648, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690852, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690879, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690882, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690921, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690948, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705690953, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691178, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691195, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691198, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691301, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691326, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691331, "dur": 503, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691842, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691870, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691873, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705691899, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692068, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692092, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692095, "dur": 831, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692930, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692933, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692963, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705692969, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693259, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693289, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693293, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693400, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693405, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693437, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693441, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693479, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693508, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693513, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693657, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693693, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693698, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693825, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693855, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693873, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705693876, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694098, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694101, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694132, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694136, "dur": 460, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694602, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694632, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694638, "dur": 228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694871, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694897, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694900, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694937, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694966, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705694970, "dur": 357, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695334, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695367, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695552, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695578, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695583, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695679, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695685, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695700, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695703, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695745, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695765, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695769, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705695998, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696020, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696024, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696041, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696045, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696089, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696106, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696109, "dur": 145, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696260, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696280, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696283, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696304, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696307, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696330, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696335, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696357, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696361, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696387, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696391, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696411, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696414, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696436, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696440, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696457, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696461, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696477, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696481, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696506, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696509, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696531, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696534, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696557, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696562, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696592, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696596, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696622, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696625, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696651, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696655, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696680, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696684, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696702, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696705, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696725, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696728, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696747, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696750, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696767, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696770, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696787, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696791, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696805, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696808, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696829, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696833, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696856, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696885, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696891, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696910, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696913, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696938, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696944, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696977, "dur": 4, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705696984, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697011, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697017, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697042, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697046, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697066, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697070, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697093, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697098, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697125, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697133, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697155, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697159, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697186, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697192, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697211, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697215, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697239, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697244, "dur": 84, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697334, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697342, "dur": 137, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697486, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697491, "dur": 114, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697611, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697616, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697737, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697741, "dur": 125, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697876, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705697885, "dur": 273, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705698164, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705698167, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705698272, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705698276, "dur": 186081, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705884374, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705884381, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705884454, "dur": 25, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705884481, "dur": 4662, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705889152, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705889157, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705889275, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705889279, "dur": 73830, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705963125, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705963133, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705963195, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705963202, "dur": 110, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705963317, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450705963322, "dur": 47567, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706010902, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706010907, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706010938, "dur": 26, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706010965, "dur": 15364, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706026341, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706026349, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706026435, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706026439, "dur": 24062, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706050515, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706050520, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706050552, "dur": 32, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706050585, "dur": 14985, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706065582, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706065587, "dur": 170, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706065764, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706065785, "dur": 1856, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706067647, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706067651, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706067737, "dur": 49, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706067792, "dur": 69648, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706137452, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706137459, "dur": 137, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706137614, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706137622, "dur": 396, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706138026, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706138030, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706138091, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706138126, "dur": 982, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706139115, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706139119, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706139234, "dur": 482, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450706139723, "dur": 28210, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 1380, "tid": 356, "ts": 1751450706180153, "dur": 5809, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1380, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1380, "tid": 8589934592, "ts": 1751450705588351, "dur": 231281, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1380, "tid": 8589934592, "ts": 1751450705819635, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1380, "tid": 8589934592, "ts": 1751450705819643, "dur": 1391, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1380, "tid": 356, "ts": 1751450706185969, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1380, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450705571582, "dur": 597422, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450705574858, "dur": 6242, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450706169222, "dur": 4252, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450706171440, "dur": 112, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450706173561, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1380, "tid": 356, "ts": 1751450706185983, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751450705597850, "dur": 1573, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450705599429, "dur": 1677, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450705601208, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751450705601346, "dur": 301, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450705602517, "dur": 745, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705603932, "dur": 1965, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450705605994, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_1236C9FFD38EBE96.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705606297, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450705606461, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705606622, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751450705606788, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_AEA6B4BCAB1E95DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705606916, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751450705607089, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751450705607245, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_09A2B486E6034DFE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705607426, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_FD54DBCE44D88AE4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705607587, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_AEBFCF2A2C8F2407.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450705607745, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751450705607852, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450705608013, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450705608195, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751450705608331, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450705612165, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751450705601669, "dur": 25232, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450705626917, "dur": 513171, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450706140094, "dur": 626, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450706140739, "dur": 53, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450706141022, "dur": 68, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450706141112, "dur": 22959, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751450705602479, "dur": 24492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705626974, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450705627494, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705627572, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450705628442, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751450705628536, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751450705628629, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751450705628849, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705628962, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751450705629354, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705629591, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705630476, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705630697, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705630902, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705631118, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705631330, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705631764, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705631987, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705632228, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705632474, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705632682, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705632912, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705633123, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705633763, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705633987, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705634225, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705634418, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705634640, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705634852, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705635078, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705635397, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705635623, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705635830, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705636197, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705636597, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705637184, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705637900, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450705638506, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705639209, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705639729, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705640371, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705640887, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705641762, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705641865, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705642544, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705642702, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450705642861, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705643262, "dur": 1703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705644966, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705646013, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450705646123, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705646480, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705647334, "dur": 35382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705682717, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705684961, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705685044, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705687554, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705687614, "dur": 2264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705689909, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705692468, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705692696, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705694966, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705695483, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450705698799, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705699042, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751450705699264, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/NativeFilePicker.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751450705699328, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450705699748, "dur": 440352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705602012, "dur": 24911, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705626979, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705627369, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705627496, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705628372, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751450705628488, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751450705628558, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751450705628803, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705628957, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751450705629103, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6307440961136846775.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751450705629360, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705629578, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705630309, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705630913, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705631323, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705631767, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705631974, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705632189, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705632425, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705632639, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705632896, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705633137, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705633342, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705633553, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705633774, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705633985, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705634262, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705634470, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705634686, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705634896, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705635131, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705635357, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705635561, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705635775, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705636000, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705636190, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705636463, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705637178, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705637870, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705638077, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705638867, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705639070, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705639302, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705640363, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705640536, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705640769, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705641407, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705641655, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705642193, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705642551, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705642701, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705643429, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705643526, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705644067, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705644165, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705645239, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705645320, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705645427, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705646342, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705646449, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705646945, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705647026, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705647326, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450705647531, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705647812, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705648728, "dur": 237667, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705890965, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751450705890692, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705891588, "dur": 71159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751450705891127, "dur": 72558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450705964745, "dur": 184, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450705965515, "dur": 47391, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450706028216, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751450706028209, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751450706028306, "dur": 111794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705603043, "dur": 23978, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705627024, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705627391, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F4BBF2BF2203F2D5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705627483, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_32AC050EF3A70AF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705628213, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751450705628801, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705629355, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705629586, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705630324, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705631405, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705631838, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705632046, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705632270, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705632488, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705632746, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705632965, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705633841, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705634079, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705634294, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705634516, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705634818, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705635082, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705635480, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705635697, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705635913, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705636174, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705636482, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705637187, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705637879, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705638112, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705638918, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705639093, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705639291, "dur": 698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705639992, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705640665, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705640941, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705641122, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705641993, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705642543, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705642699, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705642837, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705643249, "dur": 1712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705644964, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705645172, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705646010, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450705646171, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705646501, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705647332, "dur": 35382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705682716, "dur": 2238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705684955, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705685528, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705688212, "dur": 2365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705690578, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705690820, "dur": 4433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705695254, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705695326, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450705697824, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705698512, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705698873, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705699026, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705699328, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450705700181, "dur": 439908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705602060, "dur": 24882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705626989, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705627389, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_94C8A9F1371998FD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705627504, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705627999, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705628152, "dur": 6707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705634928, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705635174, "dur": 1939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705637201, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705637437, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705637862, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705638271, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705638329, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705638939, "dur": 1383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705640361, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705640541, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705640654, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705641689, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705641938, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705642696, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705642880, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705643426, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705643565, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705644304, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705644451, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705644961, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705645122, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705645706, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450705645856, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705646408, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705647336, "dur": 35387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705682724, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705684944, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705685099, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705687483, "dur": 942, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705688432, "dur": 2257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705690732, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705693374, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705695893, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450705698333, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705698718, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705699081, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705699359, "dur": 126177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450705825536, "dur": 314630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705602527, "dur": 24452, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705626981, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705627501, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705628293, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751450705628367, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751450705628599, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751450705628836, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705628974, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751450705629344, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705629555, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705630171, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705630906, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705631156, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705631377, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705631802, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705632015, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705632234, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705632448, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705632656, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705632880, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705633108, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705633323, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705633528, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705633748, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705633971, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705634209, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705634441, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705634678, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705635000, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705635232, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705635438, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705635985, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705636204, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705636727, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705637175, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705637878, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705638075, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705638679, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705639304, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705639469, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705639675, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705640248, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705640638, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705640819, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705641031, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705641615, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705641676, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705641744, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450705641940, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705642555, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705642706, "dur": 2266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705644972, "dur": 2358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705647331, "dur": 35386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705682720, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705684789, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705684983, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705687323, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705687404, "dur": 2724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705690128, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705690284, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705692372, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705692995, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705695153, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705695547, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450705698451, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705698541, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705699154, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751450705699307, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450705699366, "dur": 328875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450706028260, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751450706028242, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751450706028338, "dur": 111757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705602203, "dur": 24750, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705626956, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450705627318, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450705627502, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450705628212, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450705628404, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450705628551, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450705628838, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705628958, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450705629151, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12546371273225361319.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450705629343, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705629653, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705630491, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705630701, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705630904, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705631135, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705631358, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705631795, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705632008, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705632219, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705632429, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705632660, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705632894, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705633106, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705633328, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705633541, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705633759, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705633984, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705634239, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705634442, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705634678, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705634895, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705635194, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705635406, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705635627, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705635846, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705636074, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705636403, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705637172, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705637874, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450705638092, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705638784, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705638864, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450705639446, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705640885, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705641460, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705641556, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705641916, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705642544, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705642698, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450705642882, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705643386, "dur": 1585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705644971, "dur": 2358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705647329, "dur": 35392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705682722, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705685349, "dur": 700, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705686056, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705688919, "dur": 1544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705690469, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705692918, "dur": 2512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705695466, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450705699104, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705699320, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450705699440, "dur": 440645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705602337, "dur": 24623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705626962, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450705627510, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450705628166, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751450705628603, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751450705628794, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705629357, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705629615, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705630474, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705630687, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705630888, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705631112, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705631340, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705631796, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705631998, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705632217, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705632431, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705632651, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705632874, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705633129, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705633340, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705633535, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705633754, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705633966, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705634196, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705634391, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705634606, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705634819, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705635034, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705635366, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705635601, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705635804, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705636052, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705636262, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705636607, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705637182, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705637867, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450705638044, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705638534, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705639314, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705639464, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450705639656, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705640792, "dur": 720, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705641561, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705641690, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450705641885, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705642349, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705642536, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1751450705643037, "dur": 111, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705643290, "dur": 33617, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1751450705682711, "dur": 2271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705685027, "dur": 3801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705688829, "dur": 928, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705689763, "dur": 2666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705692430, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705692993, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705695664, "dur": 1341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450705697012, "dur": 2555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450705699643, "dur": 440465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705602411, "dur": 24554, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705626968, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705627304, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_424FA579C047A49A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705627373, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705627509, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705628058, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751450705628206, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751450705628834, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705628960, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751450705629365, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705629585, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705630402, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705630550, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705630754, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705630967, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705631208, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705631424, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705631928, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705632157, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705632372, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705632589, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705632809, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705633059, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705633279, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705633485, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705633712, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705633923, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705634160, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705634356, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705634577, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705634787, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705635116, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705635328, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705635560, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705635769, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705636000, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705636211, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705636801, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705637174, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705637870, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705638087, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705638658, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705638890, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705639099, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705639295, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705639602, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705640177, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705640351, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705640593, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705641314, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_5CA1595A93B2C59B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705641413, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705641606, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705641659, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705642294, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705642539, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705642703, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705644070, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450705644175, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705644483, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705644969, "dur": 2362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705647331, "dur": 37582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705684914, "dur": 2788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705687703, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705688504, "dur": 2538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705691042, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705691499, "dur": 3823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705695323, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705695740, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450705698463, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705698591, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705699087, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751450705699289, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705699346, "dur": 123669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705824376, "dur": 165, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1751450705824541, "dur": 907, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1751450705825449, "dur": 51, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 8, "ts": 1751450705823017, "dur": 2485, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450705825502, "dur": 314589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705602726, "dur": 24264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705626992, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705627370, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_EB030C3D9E9F35E6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705627492, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705628021, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705628201, "dur": 6280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705634560, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705634782, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705635018, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705635266, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705635529, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705635743, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705635962, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705636183, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705636501, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705637186, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705637880, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705638131, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705638700, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705638999, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705640251, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705640513, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705640734, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705641216, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705641334, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705641493, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705642100, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705642190, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450705642353, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705642773, "dur": 2197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705644970, "dur": 2364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705647335, "dur": 35391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705682730, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705685066, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705687272, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705687664, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705690014, "dur": 2556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705692614, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705694891, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705695000, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705697219, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450705697623, "dur": 2064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450705699722, "dur": 440377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705602938, "dur": 24074, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705627015, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705627314, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705627489, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705628329, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751450705628817, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705629102, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18127688178268093734.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751450705629342, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705629555, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705629729, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705630587, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705630793, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705630998, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705631221, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705631450, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705631903, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705632120, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705632331, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705632547, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705632773, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705632991, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705633225, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705633430, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705633643, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705633870, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705634107, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705634329, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705634625, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705634836, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705635161, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705635386, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705635618, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705635836, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705636049, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705636261, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705636676, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705637188, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705637881, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705638110, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705638718, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705639003, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705639176, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705639439, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705639515, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705640138, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705640381, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705640577, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705641107, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705641510, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705641962, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705642540, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705642706, "dur": 2257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705644965, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705645076, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705645363, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705645441, "dur": 1884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705647328, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450705647471, "dur": 35248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705682721, "dur": 2271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705684993, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705685501, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705687785, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705688079, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705690994, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705693244, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705695722, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450705698178, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705698426, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705698906, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705699082, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751450705699265, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/OutlineFx.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751450705699323, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450705699511, "dur": 440576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705602624, "dur": 24360, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 175145**********, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705627508, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705628097, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_AEBFCF2A2C8F2407.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705628265, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751450705628331, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751450705628706, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751450705628787, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705628954, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751450705629371, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705629584, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705630397, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705630859, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705631065, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705631291, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705631713, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705631958, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705632173, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705632422, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705632632, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705632859, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705633064, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705633276, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705633470, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705633683, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705633898, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705634128, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705634336, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705634601, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705634819, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705635083, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705635317, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705635569, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705635784, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705636035, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705636114, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705636401, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705637209, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705637864, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705638105, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705638822, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705639225, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705639789, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705640122, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705640693, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705640891, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705641046, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705641547, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705642232, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705642374, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705642689, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705642783, "dur": 2181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705644989, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450705645118, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705645202, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705645614, "dur": 1721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705647335, "dur": 36424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705683760, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705686147, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705686204, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705688360, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705688854, "dur": 2175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705691061, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705693372, "dur": 2486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705695859, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705696669, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705699364, "dur": 191373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705890758, "dur": 72027, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751450705890739, "dur": 73331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450705965072, "dur": 178, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450705965585, "dur": 86910, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450706067373, "dur": 71985, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751450706067366, "dur": 71996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751450706139378, "dur": 664, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751450705602749, "dur": 24247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705626998, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705627284, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_47EFA976E901FEC1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705627485, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE214F9E6C036FF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628041, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628145, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628244, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628576, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628642, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628766, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751450705628834, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705628959, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751450705629347, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705629556, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705630307, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705630987, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705631221, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705631453, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705631884, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705632121, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705632344, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705632548, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705632786, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705633007, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705633220, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705633415, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705633622, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705633850, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705634093, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705634296, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705634517, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705634728, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705634973, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705635168, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705635385, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705635585, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705635801, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705636041, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705636253, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705636708, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705637188, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705637873, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705638062, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705638621, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705638809, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705639091, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705639482, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705640239, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705640296, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705640847, "dur": 903, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705641775, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705642268, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705642324, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705642538, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705642667, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705642845, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705643952, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705644968, "dur": 2015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705646984, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450705647086, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705647399, "dur": 35331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705682730, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705685062, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705685178, "dur": 3157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705688380, "dur": 2340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705690720, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705690829, "dur": 2293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705693122, "dur": 780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705693924, "dur": 2911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705696835, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450705697399, "dur": 2721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450705700159, "dur": 439946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705602869, "dur": 24132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705627003, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705627496, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705628212, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751450705628333, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450705628712, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450705628799, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705628955, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450705629364, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705629606, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705630476, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705630665, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705630872, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705631106, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705631315, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705631746, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705631970, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705632201, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705632422, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705632642, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705632857, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705633154, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705633402, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705633616, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705633852, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705634835, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705635070, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705635280, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705635497, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705635703, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705635912, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705636139, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705636401, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705637173, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705637877, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705638133, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705638634, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705638748, "dur": 737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705639489, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705640011, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705640072, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705640339, "dur": 2288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705642665, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705642811, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705644044, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705644196, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705644986, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705645144, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705645738, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450705645883, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705646324, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705646465, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705647338, "dur": 35373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705682715, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705684842, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705684975, "dur": 2349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705687325, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705687535, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705689961, "dur": 1602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705691570, "dur": 4077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705695648, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705695943, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450705698741, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705698952, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450705699370, "dur": 368021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450706067409, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751450706067392, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751450706067536, "dur": 2085, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751450706069625, "dur": 70476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705602887, "dur": 24120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705627010, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705627372, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_878D99D4DAFDE3CC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705627459, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_878D99D4DAFDE3CC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705627521, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705628212, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751450705628657, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751450705628795, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705628958, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751450705629348, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705629561, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705630453, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705630655, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705630876, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705631108, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705631324, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705631804, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705632011, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705632210, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705632463, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705632686, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705632919, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705633129, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705633356, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705633557, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705633756, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705633970, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705634200, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705634426, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705634636, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705634854, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705635102, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705635309, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705635528, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705635751, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705635963, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705636485, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705637179, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705637871, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705638074, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705638905, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705638964, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705639157, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705639997, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705640300, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705640514, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705640730, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705641341, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705641553, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705642081, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705642136, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705642544, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705642702, "dur": 1345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705644048, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450705644157, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705644663, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705644977, "dur": 2363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705647340, "dur": 35402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705682742, "dur": 3423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705686166, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705686830, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705689731, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705690057, "dur": 3812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705693869, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705693931, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705696631, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450705696939, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450705699425, "dur": 440665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705602990, "dur": 24028, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705627019, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705627295, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705627493, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705628294, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751450705628442, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751450705628605, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751450705628838, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705628963, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751450705629214, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18074560900205879295.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751450705629352, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705629592, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705630572, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705630781, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705630979, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705631190, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705631804, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705632060, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705632278, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705632498, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705632729, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705632942, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705633167, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705633377, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705633595, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705634222, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705634432, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705634655, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705634870, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705635107, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705635622, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705635829, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705636057, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705636272, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705636397, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705637175, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705637869, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705638083, "dur": 1854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705640008, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705640234, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705640973, "dur": 1028, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705642011, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705642542, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705642703, "dur": 1659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705644363, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705644467, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705644766, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705645073, "dur": 2254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705647328, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450705647543, "dur": 35197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705682742, "dur": 3332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705686075, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705686328, "dur": 2757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705689119, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705691507, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450705691622, "dur": 2464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705694132, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705696955, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450705699472, "dur": 440612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705603060, "dur": 23966, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705627026, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705627514, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705628088, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751450705628446, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751450705628604, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751450705628831, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705628956, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751450705629362, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705629581, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705630517, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705630868, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705631099, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705631316, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705631744, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705631967, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705632181, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705632407, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705632621, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705632833, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705633054, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705633297, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705633513, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705633718, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705633932, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705634581, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705634785, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705635025, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705635263, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705635636, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705635853, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705636080, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705636319, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705636412, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705637171, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705637876, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705638077, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705638723, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705639637, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705640433, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705640580, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705641163, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705641320, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_98F777B9B438615B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705641418, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705641689, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705641740, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705641899, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705642567, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705642697, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450705642825, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705642922, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705643228, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705643371, "dur": 1602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705644973, "dur": 2366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705647339, "dur": 35383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705682724, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705684906, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705685469, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705687782, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705688441, "dur": 5451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705693941, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705696168, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450705698659, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705698722, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705698780, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705698886, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705699222, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705699325, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450705699651, "dur": 440432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450706167755, "dur": 1912, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1380, "tid": 356, "ts": 1751450706186489, "dur": 2876, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1380, "tid": 356, "ts": 1751450706189509, "dur": 1780, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1380, "tid": 356, "ts": 1751450706177935, "dur": 14104, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}