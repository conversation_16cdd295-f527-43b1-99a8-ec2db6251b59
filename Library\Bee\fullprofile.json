{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1380, "tid": 270, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1380, "tid": 270, "ts": 1751449697758845, "dur": 506, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1380, "tid": 270, "ts": 1751449697761949, "dur": 761, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1380, "tid": 1, "ts": 1751449697190910, "dur": 5473, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1380, "tid": 1, "ts": 1751449697196390, "dur": 112200, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1380, "tid": 1, "ts": 1751449697308607, "dur": 115006, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1380, "tid": 270, "ts": 1751449697762717, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 1380, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697189144, "dur": 7791, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697196938, "dur": 553472, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697197766, "dur": 2367, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697200141, "dur": 1486, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697201634, "dur": 252, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697201895, "dur": 37, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697201934, "dur": 90, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202030, "dur": 4, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202044, "dur": 65, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202119, "dur": 4, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202125, "dur": 401, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202535, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202541, "dur": 114, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202660, "dur": 11, "ph": "X", "name": "ProcessMessages 7329", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202674, "dur": 56, "ph": "X", "name": "ReadAsync 7329", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202734, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202738, "dur": 90, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202835, "dur": 4, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202840, "dur": 34, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202878, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202881, "dur": 98, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202983, "dur": 1, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697202988, "dur": 55, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203046, "dur": 4, "ph": "X", "name": "ProcessMessages 2792", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203051, "dur": 71, "ph": "X", "name": "ReadAsync 2792", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203132, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203135, "dur": 27, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203163, "dur": 2, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203167, "dur": 33, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203203, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203206, "dur": 37, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203253, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203257, "dur": 100, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203363, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203367, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203420, "dur": 5, "ph": "X", "name": "ProcessMessages 2126", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203427, "dur": 96, "ph": "X", "name": "ReadAsync 2126", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203529, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203533, "dur": 103, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203643, "dur": 5, "ph": "X", "name": "ProcessMessages 1945", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203649, "dur": 304, "ph": "X", "name": "ReadAsync 1945", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203959, "dur": 3, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697203965, "dur": 138, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204109, "dur": 8, "ph": "X", "name": "ProcessMessages 5442", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204119, "dur": 259, "ph": "X", "name": "ReadAsync 5442", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204386, "dur": 4, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204392, "dur": 78, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204475, "dur": 5, "ph": "X", "name": "ProcessMessages 3131", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204482, "dur": 23, "ph": "X", "name": "ReadAsync 3131", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204506, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204509, "dur": 21, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204533, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204535, "dur": 33, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204571, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204574, "dur": 86, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204667, "dur": 3, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204672, "dur": 32, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204710, "dur": 3, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204715, "dur": 23, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204740, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204742, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204764, "dur": 6, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204773, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204799, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204802, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204825, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204828, "dur": 34, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204867, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204870, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204889, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204892, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204912, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204937, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204939, "dur": 19, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204961, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697204963, "dur": 64, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205034, "dur": 3, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205039, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205073, "dur": 9, "ph": "X", "name": "ProcessMessages 1462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205088, "dur": 106, "ph": "X", "name": "ReadAsync 1462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205201, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205204, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205234, "dur": 2, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205238, "dur": 88, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205341, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205379, "dur": 2, "ph": "X", "name": "ProcessMessages 1679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205383, "dur": 20, "ph": "X", "name": "ReadAsync 1679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205405, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205407, "dur": 25, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205436, "dur": 28, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205468, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205471, "dur": 25, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205498, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205502, "dur": 16, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205520, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205522, "dur": 14, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205539, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205541, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205561, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205586, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205589, "dur": 18, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205609, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205612, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205636, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205639, "dur": 27, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205668, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205670, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205699, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205701, "dur": 28, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205731, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205734, "dur": 22, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205763, "dur": 2, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205766, "dur": 24, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205793, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205795, "dur": 20, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205817, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205820, "dur": 40, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205863, "dur": 2, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205866, "dur": 21, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205890, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205893, "dur": 20, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205916, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205945, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205947, "dur": 22, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205971, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205974, "dur": 23, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697205999, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206002, "dur": 13, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206017, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206019, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206041, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206043, "dur": 42, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206088, "dur": 2, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206091, "dur": 29, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206122, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206125, "dur": 19, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206146, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206149, "dur": 16, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206167, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206169, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206195, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206197, "dur": 22, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206221, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206224, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206244, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206247, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206270, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206276, "dur": 27, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206306, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206309, "dur": 31, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206343, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206346, "dur": 28, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206377, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206380, "dur": 29, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206412, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206415, "dur": 33, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206450, "dur": 1, "ph": "X", "name": "ProcessMessages 1112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206453, "dur": 27, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206483, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206486, "dur": 17, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206510, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206513, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206535, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206538, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206561, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206563, "dur": 38, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206604, "dur": 1, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206607, "dur": 18, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206628, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206630, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206654, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206657, "dur": 27, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206686, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206688, "dur": 26, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206716, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206719, "dur": 16, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206737, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206740, "dur": 19, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206760, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206763, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206789, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206792, "dur": 27, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206821, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206824, "dur": 20, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206846, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206849, "dur": 24, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206877, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206909, "dur": 2, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206912, "dur": 18, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206932, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206935, "dur": 23, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206960, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206963, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206987, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697206989, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207014, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207017, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207040, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207043, "dur": 22, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207067, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207072, "dur": 29, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207103, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207106, "dur": 28, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207136, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207139, "dur": 34, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207176, "dur": 2, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207179, "dur": 18, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207199, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207202, "dur": 17, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207220, "dur": 5, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207227, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207253, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207255, "dur": 20, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207278, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207280, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207304, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207307, "dur": 19, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207336, "dur": 31, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207371, "dur": 3, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207376, "dur": 22, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207404, "dur": 2, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207407, "dur": 16, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207425, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207428, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207454, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207457, "dur": 21, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207481, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207483, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207509, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207511, "dur": 17, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207530, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207533, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207551, "dur": 5, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207558, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207581, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207584, "dur": 20, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207606, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207609, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207629, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207632, "dur": 20, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207655, "dur": 38, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207695, "dur": 2, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207698, "dur": 21, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207722, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207725, "dur": 15, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207742, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207744, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207770, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207793, "dur": 27, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697207828, "dur": 192, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208024, "dur": 62, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208089, "dur": 6, "ph": "X", "name": "ProcessMessages 5050", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208096, "dur": 54, "ph": "X", "name": "ReadAsync 5050", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208164, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208169, "dur": 29, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208200, "dur": 2, "ph": "X", "name": "ProcessMessages 1702", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208203, "dur": 19, "ph": "X", "name": "ReadAsync 1702", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208224, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208227, "dur": 23, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208253, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208256, "dur": 21, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208278, "dur": 5, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208286, "dur": 14, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208303, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208305, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208335, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208338, "dur": 26, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208366, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208369, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208394, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208398, "dur": 16, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208416, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208418, "dur": 24, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208445, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208447, "dur": 20, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208470, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208473, "dur": 20, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208495, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208499, "dur": 15, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208515, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208518, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208543, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208545, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208570, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208573, "dur": 21, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208596, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208599, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208618, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208621, "dur": 28, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208651, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208653, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208679, "dur": 2, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208682, "dur": 19, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208703, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208705, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208725, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208728, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208748, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208751, "dur": 25, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208779, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208781, "dur": 20, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208803, "dur": 6, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208812, "dur": 18, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208832, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208834, "dur": 22, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208859, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208861, "dur": 29, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208893, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208895, "dur": 21, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208919, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208921, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208942, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208945, "dur": 26, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208973, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697208976, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209013, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209015, "dur": 32, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209050, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209053, "dur": 32, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209088, "dur": 2, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209091, "dur": 25, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209120, "dur": 28, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209151, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209154, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209186, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209189, "dur": 23, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209215, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209218, "dur": 24, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209243, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209246, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209275, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209278, "dur": 22, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209305, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209309, "dur": 26, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209338, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209340, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209361, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209364, "dur": 20, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209386, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209390, "dur": 21, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209413, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209416, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209435, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209437, "dur": 25, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209464, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209467, "dur": 22, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209491, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209494, "dur": 22, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209519, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209522, "dur": 17, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209541, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209543, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209568, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209570, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209594, "dur": 5, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209602, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209628, "dur": 2, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209632, "dur": 18, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209651, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209655, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209680, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209682, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209706, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209709, "dur": 20, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209731, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209734, "dur": 14, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209750, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209752, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209778, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209801, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209804, "dur": 20, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209827, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209829, "dur": 20, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209853, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209855, "dur": 17, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209874, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209877, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209903, "dur": 5, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209910, "dur": 20, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209933, "dur": 5, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209940, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209961, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209964, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209993, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697209996, "dur": 27, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210025, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210028, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210053, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210056, "dur": 16, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210073, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210076, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210104, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210108, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210132, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210135, "dur": 20, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210157, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210160, "dur": 19, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210181, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210184, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210203, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210205, "dur": 18, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210225, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210228, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210251, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210253, "dur": 19, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210275, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210277, "dur": 15, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210294, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210297, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210326, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210328, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210353, "dur": 1, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210355, "dur": 19, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210376, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210379, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210401, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210403, "dur": 20, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210426, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210428, "dur": 20, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210450, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210453, "dur": 18, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210473, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210475, "dur": 16, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210495, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210497, "dur": 31, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210531, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210534, "dur": 20, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210557, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210560, "dur": 19, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210581, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210584, "dur": 19, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210605, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210607, "dur": 26, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210636, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210639, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210660, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210662, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210682, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210684, "dur": 20, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210707, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210709, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210731, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210734, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210754, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210757, "dur": 18, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210777, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210779, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210799, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210801, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210824, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210831, "dur": 19, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210856, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210860, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210880, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210882, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210905, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210908, "dur": 21, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210932, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210934, "dur": 19, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210956, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210959, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210983, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697210986, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211008, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211010, "dur": 22, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211034, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211041, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211065, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211067, "dur": 25, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211094, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211097, "dur": 26, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211126, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211128, "dur": 25, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211156, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211158, "dur": 15, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211175, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211177, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211204, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211207, "dur": 19, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211228, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211230, "dur": 24, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211257, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211259, "dur": 26, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211288, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211291, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211313, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211316, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211336, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211339, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211365, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211367, "dur": 28, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211397, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211401, "dur": 22, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211425, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211428, "dur": 21, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211452, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211454, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211481, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211484, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211512, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211514, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211536, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211539, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211560, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211563, "dur": 105, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211672, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211675, "dur": 27, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211704, "dur": 2, "ph": "X", "name": "ProcessMessages 1422", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211708, "dur": 20, "ph": "X", "name": "ReadAsync 1422", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211730, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211733, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211755, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211757, "dur": 29, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211790, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211793, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211810, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211812, "dur": 54, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211871, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211909, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211912, "dur": 21, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211935, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211938, "dur": 26, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211966, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211972, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697211998, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212001, "dur": 15, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212018, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212020, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212075, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212102, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212106, "dur": 17, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212126, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212128, "dur": 16, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212149, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212176, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212179, "dur": 26, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212207, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212210, "dur": 14, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212226, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212228, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212252, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212254, "dur": 50, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212309, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212333, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212336, "dur": 20, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212358, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212361, "dur": 54, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212420, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212442, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212445, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212471, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212474, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212497, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212499, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212540, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212561, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212563, "dur": 28, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212594, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212596, "dur": 50, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212652, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212674, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212677, "dur": 17, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212697, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212700, "dur": 67, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212781, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212809, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212813, "dur": 40, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212857, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212881, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212884, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212909, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212911, "dur": 55, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212970, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212972, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697212999, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213003, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213025, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213027, "dur": 56, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213088, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213117, "dur": 2, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213120, "dur": 18, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213140, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213143, "dur": 54, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213201, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213222, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213224, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213252, "dur": 5, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213260, "dur": 45, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213310, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213333, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213336, "dur": 23, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213361, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213364, "dur": 49, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213417, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213440, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213442, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213462, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213466, "dur": 54, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213523, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213548, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213551, "dur": 20, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213573, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213576, "dur": 54, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213634, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213659, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213662, "dur": 20, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213684, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213690, "dur": 12, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213705, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213707, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213752, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213784, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213787, "dur": 19, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213808, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213810, "dur": 45, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213860, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213881, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213884, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213905, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213908, "dur": 22, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213932, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213935, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697213983, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214010, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214013, "dur": 16, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214030, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214033, "dur": 53, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214090, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214113, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214116, "dur": 19, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214138, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214141, "dur": 52, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214198, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214228, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214232, "dur": 17, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214250, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214253, "dur": 49, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214306, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214334, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214337, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214359, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214362, "dur": 43, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214410, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214433, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214435, "dur": 19, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214457, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214460, "dur": 51, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214516, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214540, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214543, "dur": 24, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214570, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214573, "dur": 39, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214615, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214674, "dur": 2, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214678, "dur": 22, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214702, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214704, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214724, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214727, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214752, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214755, "dur": 17, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214777, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214781, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214831, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214856, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214859, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214880, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214883, "dur": 52, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214939, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214967, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214970, "dur": 19, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214992, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697214994, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215023, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215026, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215052, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215055, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215073, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215075, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215099, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215102, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215153, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215187, "dur": 2, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215190, "dur": 16, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215207, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215210, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215264, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215291, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215294, "dur": 20, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215315, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215318, "dur": 48, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215370, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215406, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215409, "dur": 17, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215429, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215431, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215482, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215500, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215502, "dur": 24, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215529, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215535, "dur": 27, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215566, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215569, "dur": 14, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215590, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215593, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215629, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215652, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215655, "dur": 15, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215671, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215674, "dur": 24, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215700, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215702, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215727, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215730, "dur": 18, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215750, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215753, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215769, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215771, "dur": 53, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215829, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215851, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215854, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215876, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215879, "dur": 49, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215933, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215956, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215959, "dur": 19, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215981, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697215984, "dur": 22, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216009, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216011, "dur": 21, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216034, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216036, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216058, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216064, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216091, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216094, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216134, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216155, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216158, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216177, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216179, "dur": 20, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216202, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216204, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216250, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216274, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216277, "dur": 24, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216303, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216306, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216335, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216338, "dur": 22, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216363, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216366, "dur": 14, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216382, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216384, "dur": 26, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216412, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216415, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216460, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216483, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216486, "dur": 17, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216505, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216508, "dur": 52, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216565, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216590, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216593, "dur": 15, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216609, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216612, "dur": 58, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216674, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216695, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216698, "dur": 24, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216724, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216736, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216755, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216759, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216791, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216818, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216821, "dur": 29, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216853, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216855, "dur": 22, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216880, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216883, "dur": 22, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216908, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216911, "dur": 16, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216928, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216931, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216952, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697216959, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217004, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217033, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217037, "dur": 15, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217054, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217056, "dur": 51, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217111, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217136, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217139, "dur": 19, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217160, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217163, "dur": 51, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217218, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217247, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217250, "dur": 26, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217278, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217281, "dur": 37, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217324, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217346, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217349, "dur": 16, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217367, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217369, "dur": 60, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217434, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217464, "dur": 2, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217468, "dur": 22, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217492, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217495, "dur": 31, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217528, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217531, "dur": 25, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217559, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217562, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217582, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217585, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217635, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217657, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217660, "dur": 24, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217687, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217689, "dur": 15, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217706, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217708, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217749, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217771, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217774, "dur": 14, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217790, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217792, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217814, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217816, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217865, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217901, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217904, "dur": 41, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217947, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217950, "dur": 27, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217980, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697217983, "dur": 179, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218167, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218205, "dur": 12, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218220, "dur": 47, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218270, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218272, "dur": 27, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218302, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218305, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218347, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218381, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218384, "dur": 28, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218415, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218418, "dur": 34, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218456, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218488, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218491, "dur": 26, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218519, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218522, "dur": 39, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218565, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218589, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218592, "dur": 24, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218618, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218621, "dur": 48, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218673, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218696, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218699, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218723, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218726, "dur": 31, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218760, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218763, "dur": 28, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218793, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218796, "dur": 23, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218821, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218823, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218847, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218850, "dur": 33, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218888, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218912, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218915, "dur": 20, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218937, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218940, "dur": 51, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697218995, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219028, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219031, "dur": 32, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219065, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219068, "dur": 32, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219103, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219105, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219142, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219178, "dur": 2, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219181, "dur": 23, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219207, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219209, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219252, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219277, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219280, "dur": 18, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219302, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219305, "dur": 56, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219365, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219368, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219404, "dur": 2, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219407, "dur": 21, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219432, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219436, "dur": 71, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219513, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219545, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219549, "dur": 35, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219588, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219592, "dur": 42, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219638, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219641, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219678, "dur": 5, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219685, "dur": 25, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219713, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219717, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219757, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219792, "dur": 2, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219796, "dur": 30, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219829, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219833, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219876, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219909, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219912, "dur": 31, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219947, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219950, "dur": 44, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697219999, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220034, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220037, "dur": 30, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220070, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220073, "dur": 37, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220116, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220152, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220155, "dur": 28, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220186, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220189, "dur": 40, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220233, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220271, "dur": 2, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220274, "dur": 28, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220306, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220309, "dur": 30, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220343, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220374, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220377, "dur": 28, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220408, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220411, "dur": 21, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220434, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220438, "dur": 19, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220460, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220463, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220487, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220489, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220545, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220574, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220578, "dur": 20, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220600, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220602, "dur": 53, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220661, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220690, "dur": 6, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220699, "dur": 16, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220722, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220724, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220768, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220790, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220793, "dur": 17, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220814, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220816, "dur": 14, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220832, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220834, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220885, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220912, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220915, "dur": 21, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220943, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220947, "dur": 44, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697220995, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221019, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221022, "dur": 20, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221045, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221048, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221074, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221078, "dur": 32, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221112, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221115, "dur": 17, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221134, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221136, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221165, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221168, "dur": 45, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221217, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221219, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221243, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221246, "dur": 24, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221272, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221274, "dur": 89, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221375, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221404, "dur": 3, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221409, "dur": 23, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221434, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221436, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221463, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221466, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221493, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221496, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221544, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221546, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221568, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221571, "dur": 14, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221587, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221590, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221658, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221698, "dur": 2, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221701, "dur": 26, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221730, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221732, "dur": 32, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221770, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221804, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221807, "dur": 24, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221833, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221836, "dur": 39, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221878, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221881, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221916, "dur": 2, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221919, "dur": 30, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221952, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221955, "dur": 30, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697221989, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222021, "dur": 2, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222024, "dur": 24, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222052, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222055, "dur": 45, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222104, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222127, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222130, "dur": 25, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222159, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222163, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222192, "dur": 2, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222195, "dur": 29, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222227, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222230, "dur": 28, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222260, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222263, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222292, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222295, "dur": 38, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222336, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222339, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222376, "dur": 3, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222385, "dur": 26, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222414, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222417, "dur": 36, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222459, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222491, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222494, "dur": 34, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222531, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222534, "dur": 29, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222565, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222569, "dur": 31, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222603, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222606, "dur": 28, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222638, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222641, "dur": 28, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222673, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222705, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222708, "dur": 34, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222746, "dur": 2, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222749, "dur": 29, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222781, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222784, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222812, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222814, "dur": 16, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222833, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222835, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222895, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222934, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222937, "dur": 40, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222979, "dur": 2, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697222982, "dur": 34, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223019, "dur": 2, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223022, "dur": 29, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223061, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223065, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223105, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223144, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223150, "dur": 42, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223196, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223199, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223236, "dur": 458, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223701, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223766, "dur": 15, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223785, "dur": 32, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223823, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223830, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223865, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223871, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223902, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223908, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223947, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223953, "dur": 32, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223989, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697223994, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224024, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224030, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224070, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224078, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224110, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224117, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224156, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224162, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224209, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224217, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224255, "dur": 4, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224261, "dur": 27, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224293, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224298, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224336, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224344, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224381, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224388, "dur": 34, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224428, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224435, "dur": 40, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224479, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224487, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224528, "dur": 6, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224537, "dur": 34, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224573, "dur": 6, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224581, "dur": 28, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224614, "dur": 4, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224621, "dur": 33, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224660, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224668, "dur": 39, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224714, "dur": 5, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224722, "dur": 46, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224773, "dur": 7, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224782, "dur": 37, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224824, "dur": 7, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224834, "dur": 28, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224865, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224872, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224914, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224921, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224955, "dur": 3, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224959, "dur": 27, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224990, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697224994, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225025, "dur": 4, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225031, "dur": 35, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225071, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225078, "dur": 44, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225128, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225135, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225174, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225181, "dur": 34, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225219, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225226, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225257, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225267, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225302, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225306, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225342, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225377, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225384, "dur": 36, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225425, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225433, "dur": 41, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225479, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225487, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225526, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225534, "dur": 34, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225573, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225580, "dur": 32, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225618, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225627, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225660, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225665, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225698, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225707, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225734, "dur": 4, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225740, "dur": 23, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225768, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225773, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225805, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225811, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225838, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225843, "dur": 69, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225917, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697225922, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226183, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226207, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226210, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226354, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226376, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697226380, "dur": 4847, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697231240, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697231248, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697231286, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697231290, "dur": 1522, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697232821, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697232826, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697232868, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697232874, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697232900, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697232904, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697233100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697233103, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697233124, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697233127, "dur": 1092, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234226, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234231, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234260, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234264, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234322, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234326, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234361, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234364, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234622, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234625, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234661, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234666, "dur": 178, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234853, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234878, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234883, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234913, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234919, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234951, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234968, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697234971, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235046, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235069, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235072, "dur": 161, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235240, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235244, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235266, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235269, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235426, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235450, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235454, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235477, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235481, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235500, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235503, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235524, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235529, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235550, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235554, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235662, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235791, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235796, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235821, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235823, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235847, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235851, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235871, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235874, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235901, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235906, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235931, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697235935, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236038, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236043, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236066, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236070, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236146, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236152, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236172, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236175, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236189, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236192, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236233, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236236, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236267, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236294, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236298, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236328, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236333, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236382, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236385, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236402, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236404, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236421, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236423, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236457, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236474, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236477, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236546, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236549, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236574, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236578, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236614, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236637, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236640, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236941, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236962, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697236966, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237023, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237046, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237051, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237110, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237132, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237136, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237158, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237175, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237177, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237202, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237223, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237227, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237253, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237273, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237277, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237304, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237307, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237328, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237333, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237350, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237354, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237389, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237406, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237409, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237497, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237501, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237519, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237523, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237546, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237551, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237571, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237574, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237612, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237726, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237731, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237746, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697237750, "dur": 337, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238096, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238146, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238150, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238173, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238177, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238198, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238203, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238225, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238229, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238243, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238246, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238327, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238354, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238358, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238377, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238380, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238406, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238414, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238445, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238449, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238473, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238477, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238500, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238539, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238558, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238563, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238579, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238582, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238601, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238605, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238746, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238767, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238771, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238786, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238788, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238806, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697238810, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239081, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239084, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239113, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239118, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239149, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239155, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239184, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239189, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239258, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239275, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239278, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239304, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239309, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239333, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239336, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239390, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239412, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239418, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239449, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239453, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239488, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239493, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239552, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239571, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239575, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239609, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239614, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239639, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239643, "dur": 116, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239764, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239768, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239790, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239793, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239851, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239871, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239875, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239891, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239942, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239958, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697239962, "dur": 181, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240147, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240151, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240169, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240172, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240219, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240238, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240243, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240261, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240263, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240284, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240287, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240487, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240520, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240525, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240567, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240573, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240597, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240601, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240654, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697240683, "dur": 740, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241429, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241483, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241490, "dur": 25, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241520, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241524, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241551, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241554, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241584, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241587, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241634, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241638, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241667, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241670, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241723, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241726, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241755, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241758, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241785, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241789, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241812, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241815, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241955, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241959, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241992, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697241997, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242095, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242099, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242194, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242200, "dur": 85, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242289, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242292, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242389, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242475, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242480, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242570, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242573, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242721, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242729, "dur": 66, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242798, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242802, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242880, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242884, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242905, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697242907, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243049, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243053, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243075, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243079, "dur": 122, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243206, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243222, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243315, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243319, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243339, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243341, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243358, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243380, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243385, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243403, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243529, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243533, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243554, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243557, "dur": 171, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243733, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243798, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243802, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243869, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243931, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697243935, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244025, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244077, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244081, "dur": 432, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244524, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244529, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244615, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244618, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244689, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697244699, "dur": 463, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245168, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245204, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245207, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245248, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245277, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245279, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245308, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245314, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245538, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245543, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245581, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245584, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245627, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245632, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245704, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245724, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245727, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245813, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245900, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697245904, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246009, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246013, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246033, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246036, "dur": 714, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246758, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246776, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697246781, "dur": 27274, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697274066, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697274071, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697274098, "dur": 1650, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697275755, "dur": 6231, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697281998, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282006, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282043, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282049, "dur": 35, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282091, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282096, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282133, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282138, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282208, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282211, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282233, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282236, "dur": 296, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282539, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282556, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282559, "dur": 372, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282940, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697282964, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697283600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697283603, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697283619, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697283622, "dur": 616, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284245, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284264, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284267, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284375, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284393, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284396, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284411, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284414, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284444, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284449, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284475, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284478, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284586, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284591, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284617, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284620, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284642, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284646, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284879, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284884, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697284908, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285072, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285098, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285146, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285178, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285183, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285307, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285327, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285330, "dur": 597, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285935, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285940, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285988, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697285993, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286194, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286217, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286220, "dur": 551, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286780, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286785, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286819, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286836, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286840, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286890, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286911, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697286914, "dur": 302, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287226, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287248, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287251, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287288, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287292, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287313, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287317, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287371, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287389, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287392, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287972, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697287978, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288004, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288008, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288050, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288055, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288079, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288082, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288342, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288383, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288664, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288687, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288703, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697288705, "dur": 426, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289137, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289140, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289156, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289159, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289364, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289388, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289392, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289555, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289572, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697289575, "dur": 676, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290258, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290274, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290277, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290354, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290377, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290381, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290408, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290412, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290520, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290537, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290539, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290761, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290765, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290783, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290787, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290804, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290808, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290854, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290871, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290874, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290896, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290949, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290964, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697290967, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291020, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291039, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291075, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291091, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291093, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291495, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291516, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291640, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291656, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697291661, "dur": 614, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292281, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292296, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292299, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292531, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697292550, "dur": 592, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293146, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293149, "dur": 137, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293293, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293312, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293315, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293344, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293347, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293364, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293367, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293385, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293388, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293436, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293452, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293455, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293517, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293534, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293537, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293850, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293867, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293871, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293974, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293988, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697293990, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294222, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294244, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294395, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294414, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294418, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294435, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294438, "dur": 457, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294902, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294935, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294970, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697294975, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295002, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295005, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295023, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295026, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295043, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295046, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295067, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295070, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295097, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295102, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295128, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295132, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295158, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295346, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295364, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295368, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295392, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295397, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295418, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295421, "dur": 265, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295694, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295728, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295732, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295758, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295763, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295788, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295793, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295814, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295818, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295840, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295844, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295868, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295872, "dur": 16, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295891, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295895, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295913, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295917, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295934, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295937, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295959, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295963, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295981, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697295984, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296006, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296027, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296030, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296049, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296052, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296073, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296078, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296101, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296105, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296135, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296139, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296159, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296162, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296185, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296189, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296208, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296211, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296234, "dur": 6, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296243, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296266, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296271, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296294, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296298, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296323, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296328, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296359, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296362, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296397, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296403, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296430, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296436, "dur": 15, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296454, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296458, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296486, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296492, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296522, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296529, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296551, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296556, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296595, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296600, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296624, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296626, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296650, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296652, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296784, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296789, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296825, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296829, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296858, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296861, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296944, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697296948, "dur": 189335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697486295, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697486300, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697486437, "dur": 23, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697486463, "dur": 3955, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697490438, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697490443, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697490563, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697490567, "dur": 45139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535720, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535726, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535753, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535757, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535801, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535833, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697535836, "dur": 75770, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697611621, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697611631, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697611700, "dur": 43, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697611746, "dur": 13411, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697625170, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697625177, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697625224, "dur": 32, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697625258, "dur": 4428, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697629698, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697629704, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697629865, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697629874, "dur": 13976, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697643863, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697643869, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697643940, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697643952, "dur": 1383, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697645346, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697645353, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697645511, "dur": 45, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697645558, "dur": 74195, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697719767, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697719774, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697719828, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697719834, "dur": 455, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697720297, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697720302, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697720350, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697720387, "dur": 1040, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697721439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697721444, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697721525, "dur": 506, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751449697722036, "dur": 28138, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 1380, "tid": 270, "ts": 1751449697762738, "dur": 5608, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1380, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1380, "tid": 8589934592, "ts": 1751449697186621, "dur": 237062, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1380, "tid": 8589934592, "ts": 1751449697423687, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1380, "tid": 8589934592, "ts": 1751449697423696, "dur": 1144, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1380, "tid": 270, "ts": 1751449697768351, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1380, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1380, "tid": 4294967296, "ts": 1751449697171196, "dur": 580104, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751449697174301, "dur": 5729, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751449697751546, "dur": 4391, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751449697754081, "dur": 105, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751449697756026, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1380, "tid": 270, "ts": 1751449697768368, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751449697196491, "dur": 1570, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697198067, "dur": 1810, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697199991, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751449697200130, "dur": 390, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697201375, "dur": 696, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697202725, "dur": 1698, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751449697204505, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_189352823CD57890.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697204609, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751449697205117, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_AEA6B4BCAB1E95DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697205308, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697205553, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751449697205768, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751449697205935, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_EBB1919501649DC3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697206110, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_C2E5A21D32AE5F0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697206216, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751449697206536, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_B73CFB0D33CD813E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697206690, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751449697206969, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751449697207143, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751449697207606, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751449697210591, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751449697200537, "dur": 25109, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697225664, "dur": 497204, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697722871, "dur": 467, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697723338, "dur": 281, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697723685, "dur": 104, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697723927, "dur": 23456, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751449697200735, "dur": 24936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697225753, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697226009, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697226242, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_EAC3CE9D642351D7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697226299, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697226353, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_EAC3CE9D642351D7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697226823, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697227467, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751449697227818, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697228063, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18127688178268093734.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751449697228152, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697228362, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697228595, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697229266, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697230052, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697230264, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697230554, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697231002, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697231202, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697231421, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697231613, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697231824, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697232028, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697232213, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697232420, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697232673, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697232870, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697233316, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697233532, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697233752, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697233966, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697234191, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697234421, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697234647, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697234866, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697235070, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697235275, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697235522, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697235722, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697236170, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697236775, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697237202, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697237376, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697238074, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697239564, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697239881, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697240068, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697240733, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697241117, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697241290, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697241878, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697241964, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697242203, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697243056, "dur": 1849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697244906, "dur": 2801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697247712, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751449697247831, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697248141, "dur": 34046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697282191, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697284476, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697284542, "dur": 3804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697288347, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697288469, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697290862, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697290912, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697291206, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697293555, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697293625, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697295933, "dur": 2615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751449697298596, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697298661, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751449697298723, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697298924, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697299023, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697299107, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751449697299172, "dur": 423701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697200940, "dur": 24756, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697225699, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697225984, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697226132, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697226303, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697226440, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697227128, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751449697227410, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751449697227527, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751449697227785, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697227950, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751449697228209, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15123065923413756060.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751449697228378, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697228607, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697229554, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697229782, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697230168, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697230387, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697230593, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697231042, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697231253, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697231484, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697231695, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697231903, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697232196, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697232400, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697232616, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697232821, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697233049, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697233246, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697233463, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697233670, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697233868, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697234071, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697234290, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697234509, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697234722, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697234937, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697235143, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697235701, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697235989, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697236752, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697237211, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697237391, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697237933, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697238432, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697239094, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697239751, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697240701, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697241453, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697241691, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697241975, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697242205, "dur": 1999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697244205, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697244323, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697244710, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697245039, "dur": 3062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697248107, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751449697248321, "dur": 33868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697282191, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697284508, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697286797, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697287167, "dur": 3390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697290592, "dur": 2420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697293067, "dur": 2599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697295695, "dur": 3309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751449697299113, "dur": 128265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697428747, "dur": 171, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1751449697428918, "dur": 927, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1751449697427379, "dur": 2515, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751449697429894, "dur": 292968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697200828, "dur": 24857, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697225687, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697226054, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697226302, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697226507, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_26C00F9C5DF6EF10.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697227061, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751449697227492, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751449697227650, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751449697227809, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697228280, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12277024429136440695.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751449697228341, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697228550, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697229236, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697229895, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697230117, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697230349, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697231003, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697231206, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697231430, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697231639, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697231855, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697232373, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697232590, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697232794, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697233005, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697233212, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697233418, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697233634, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697233832, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697234067, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697234318, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697234524, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697234735, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697234960, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697235152, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697235929, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697236753, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697237209, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697237400, "dur": 1908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697239309, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697239685, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697239933, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697240100, "dur": 1788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697241889, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697242185, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697242496, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697243600, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751449697243693, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697244077, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697244907, "dur": 3207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697248114, "dur": 34083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697282198, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697284556, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697287013, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697287681, "dur": 2501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697290182, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697290509, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697292805, "dur": 2784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697295590, "dur": 1347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751449697296943, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751449697299399, "dur": 423486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697200791, "dur": 24888, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697225746, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697226015, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697226319, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697226821, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697226966, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697227037, "dur": 6662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697233792, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697234000, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697234209, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697234480, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697234687, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697234901, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697235106, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697235924, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697236759, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697237218, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697237430, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697237929, "dur": 800, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697238734, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_444C51352B70AA18.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697238864, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697239115, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697239662, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697239750, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697240366, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697240749, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751449697240906, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697241289, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697241935, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751449697242682, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697242817, "dur": 210, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697243201, "dur": 33373, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751449697282177, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697284520, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697286795, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697289314, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697289383, "dur": 2493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697291912, "dur": 2326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697294239, "dur": 1538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697295783, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751449697298088, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697298396, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697298833, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697299064, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751449697299117, "dur": 130821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751449697429939, "dur": 292928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697201127, "dur": 24582, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697225711, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751449697226332, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751449697227292, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751449697227444, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751449697227565, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751449697227772, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697228006, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14507123614329245381.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751449697228079, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697228244, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2669672505582432988.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751449697228300, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697228893, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697229763, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697229985, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697230193, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697230396, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697230611, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697231060, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697231263, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697231472, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697231676, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697231889, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697232102, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697232303, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697232689, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697232902, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697233127, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697233354, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697233580, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697233879, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697234087, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697234298, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697234523, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697234729, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697234924, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697235153, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697235415, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697235559, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697235751, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697236349, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697236762, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697237405, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751449697238030, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697238719, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697238884, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697239160, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697239851, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751449697240014, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697240179, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697240794, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751449697241035, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697241702, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697241822, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697241979, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697242204, "dur": 1912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697244117, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751449697244273, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697244662, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697244923, "dur": 3181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697248105, "dur": 34076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697282182, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697285136, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697285491, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697287848, "dur": 5437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697293338, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697295667, "dur": 1299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697296974, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697297683, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697298482, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697298593, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697298665, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697298726, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751449697298942, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751449697299069, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751449697299120, "dur": 193314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697492452, "dur": 43547, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751449697492436, "dur": 44740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697538137, "dur": 194, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751449697538639, "dur": 89005, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751449697646084, "dur": 76081, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751449697646075, "dur": 76092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751449697722185, "dur": 636, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751449697200932, "dur": 24758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697225693, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697226302, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697226877, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751449697227194, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751449697227804, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697228112, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697228364, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697228594, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697229327, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697229462, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697229648, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697229834, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697230032, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697230237, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697230438, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697230642, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697231091, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697231325, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697231558, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697231767, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697231984, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697232640, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697233137, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697233369, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697233862, "dur": 1425, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\TMP_TextProcessingCommon.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751449697233566, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697235485, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697235544, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697235748, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697236375, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697236780, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697237221, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697237413, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697237596, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697238351, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697238506, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697238588, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697238779, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697239963, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697240172, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697240320, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697240636, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697241264, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697241348, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697241968, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697242202, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697242397, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697243785, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697243939, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697244527, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697244632, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697245004, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697245133, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751449697245248, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697245566, "dur": 2563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697248129, "dur": 34057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697282186, "dur": 2288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697284510, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697286922, "dur": 2451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697289373, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697289432, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697291807, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697292093, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697294494, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697294827, "dur": 2213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751449697297041, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697297607, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697297920, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697298502, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697298870, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751449697299125, "dur": 332984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751449697632129, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751449697632111, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751449697632213, "dur": 90656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697201278, "dur": 24444, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697225724, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697226120, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E3AA45DCF433BC79.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697226288, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697226877, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697227109, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751449697227327, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751449697227491, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751449697227572, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751449697227752, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751449697227811, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697228163, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697228331, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697228524, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697228721, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697229691, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697229883, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697230085, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697230332, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697230545, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697230995, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697231200, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697231428, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697231626, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697232061, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697232276, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697232983, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697233189, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697233404, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697233611, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697233824, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697234021, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697234234, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697234466, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697234674, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697234882, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697235080, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697235291, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697235533, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697235777, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697236136, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697236772, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697237212, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697237399, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697237491, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697238361, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697238464, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697239038, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697239094, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697239596, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697239656, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697239836, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697240153, "dur": 1725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697241879, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697241978, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697242695, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697242763, "dur": 2150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697244913, "dur": 3190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697248104, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751449697248323, "dur": 33861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697282189, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697284257, "dur": 1883, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697286147, "dur": 3188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697289367, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697292010, "dur": 929, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697292947, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697295087, "dur": 2777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751449697297865, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697297952, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697298303, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697298376, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697298585, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751449697298825, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751449697299142, "dur": 423722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697200997, "dur": 24706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697225706, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697225983, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697226228, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CD971E07FD450EB6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697226440, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_06CB192F9C3927A4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697226900, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697227015, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751449697227351, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751449697227494, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751449697227603, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751449697227753, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751449697227815, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697227961, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751449697228371, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697228570, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697229259, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697229878, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697230079, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697230294, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697230512, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697230974, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697231170, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697231391, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697231915, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697232133, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697232327, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697232555, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697232951, "dur": 920, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\GPUDriven\\InstanceData\\GPUInstanceDataUploader.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751449697232771, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697233903, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697234116, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697234329, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697234546, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697234768, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697234971, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697235181, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697235397, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697235593, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697235853, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697235930, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697236760, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697237200, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697237785, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697238755, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697238849, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697239571, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697240244, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697240694, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697240752, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697240934, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697240996, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697241582, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697241693, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697241972, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697242180, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751449697242504, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697243089, "dur": 1823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697244913, "dur": 3217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697248130, "dur": 34062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697282196, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697284517, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697286990, "dur": 2718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697289766, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697293499, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697295896, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751449697298343, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697298483, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697298619, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697299108, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751449697299221, "dur": 423665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697201193, "dur": 24522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697225718, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697226312, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697227013, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751449697227103, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751449697227304, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751449697227468, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751449697227787, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697228365, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697228578, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697229303, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697229952, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697230169, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697230370, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697231048, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697231249, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697231489, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697231689, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697231896, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697232183, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697232380, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697232593, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697232796, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697233044, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697233244, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697233465, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697233666, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697233862, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697234075, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697234321, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697234526, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697234737, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697234957, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697235155, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697235414, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697235929, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697236772, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697237207, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697237433, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697237970, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C358B5C2FFF3B9D0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697238060, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697238246, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697238429, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697238819, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697239495, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697239674, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697239847, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697240045, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697240840, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697241014, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697241149, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697241323, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697241968, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697242106, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697242305, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697242876, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697243075, "dur": 1825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697244902, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697245089, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697245929, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751449697246086, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697246591, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697248108, "dur": 34074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697282185, "dur": 2862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697285082, "dur": 2567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697287652, "dur": 1071, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697288730, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697291115, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697291244, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697293484, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697293568, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697295836, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697296081, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697298737, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751449697298870, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751449697299140, "dur": 423727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697201375, "dur": 24409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697225785, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697226057, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_424FA579C047A49A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697226375, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_634E261A11766DA3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697227106, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751449697227332, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751449697227806, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697227913, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751449697228070, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7013866772824065873.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751449697228121, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17854342601108681021.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751449697228354, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2617961040171231352.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751449697228460, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697228709, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697229986, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697230477, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697230990, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697231191, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697231420, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697231614, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697231820, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697232018, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697232210, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697232417, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697232627, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697232816, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697233057, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697233469, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697233673, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697233888, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697234089, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697234289, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697234504, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697234754, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Devices\\Sensor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751449697234706, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697235645, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697235928, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697236755, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697237210, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697237384, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697237594, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697238776, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697239017, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697239505, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697239749, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697240034, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697240108, "dur": 1670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697241779, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697241856, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697242490, "dur": 2040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697244531, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751449697244650, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697245100, "dur": 3015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697248116, "dur": 34062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697282184, "dur": 2532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697284752, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697287354, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697287616, "dur": 3826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697291443, "dur": 1953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697293403, "dur": 2883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697296287, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697296394, "dur": 2481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751449697298945, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751449697299237, "dur": 423657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697201534, "dur": 24227, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697225764, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751449697226308, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751449697226803, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751449697226969, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751449697227155, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751449697227247, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751449697227490, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751449697227810, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697227949, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751449697228204, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11221626692241425917.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751449697228342, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697228535, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697228712, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697229759, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697229981, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697230636, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697231070, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697231407, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\TimelineEditor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751449697231265, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697232003, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697232197, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697232392, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697232596, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697232811, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697233057, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697233265, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697233485, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697233691, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697233911, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697234111, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697234317, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697234753, "dur": 1100, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\UITKAssetEditor\\Views\\ContextMenu.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751449697234553, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697235931, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697236751, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697237404, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751449697237578, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697237998, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697239271, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697239496, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751449697239869, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697240246, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697241084, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751449697241287, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751449697241479, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697242102, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697242632, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697243042, "dur": 1866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697244908, "dur": 3203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697248111, "dur": 36321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697284433, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697287274, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697287415, "dur": 2668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697290084, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697290517, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697293103, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751449697293427, "dur": 3316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697296768, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751449697299168, "dur": 423702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697201316, "dur": 24418, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697225736, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751449697226326, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751449697226478, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E43C351DB48DBD9B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751449697226880, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751449697227109, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751449697227299, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751449697227493, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751449697227772, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697228306, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11474151696266288451.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751449697228370, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697228591, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697229424, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697229627, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697229808, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697229998, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697230211, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697230414, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697230617, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697231067, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697231277, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697231484, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697231691, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697231884, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697232090, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697232288, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697232503, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697232698, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697232909, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697233133, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697233346, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697233923, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697234126, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697234332, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697234550, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697234747, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697234971, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697235168, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697235513, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697235582, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697235758, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697236294, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697236758, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697237407, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751449697237588, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697238157, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697238426, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697239482, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697240066, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697240846, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697241002, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697241683, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697241983, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697242203, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751449697242771, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697243381, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697244904, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697245984, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751449697246094, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697246429, "dur": 1688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697248117, "dur": 34081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697282201, "dur": 2264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697284496, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697284605, "dur": 5413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697290019, "dur": 855, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697290875, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697290929, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697293436, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751449697294187, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697296524, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751449697299070, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751449697299137, "dur": 423728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697201477, "dur": 24277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697225757, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697226301, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697226370, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697226596, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697226825, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697226964, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697227026, "dur": 8280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697235393, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697235636, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697236771, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697236862, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697237198, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697238048, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697238976, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697239678, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697239834, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697240088, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697240748, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697240939, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697242177, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697242396, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697242989, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697243134, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697243277, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697244112, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697244265, "dur": 1570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697245879, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697246029, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697247062, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697247168, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697247694, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697247789, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697248102, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751449697248242, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697248549, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697249531, "dur": 239228, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697492675, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751449697492403, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697493431, "dur": 42476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751449697492875, "dur": 43889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697537801, "dur": 173, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751449697538601, "dur": 75465, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751449697632082, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751449697632075, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751449697632191, "dur": 90713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697201587, "dur": 24179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697225769, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697226296, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697226360, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697226793, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751449697227155, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751449697227414, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751449697227528, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751449697227622, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751449697227765, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697227962, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751449697228350, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6699892698108865450.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751449697228720, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697229841, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697230044, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697230603, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697231177, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697231381, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697231607, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697231823, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697232042, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697232257, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697232482, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697232697, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697232895, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697233124, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697233331, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697233556, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697233753, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697233957, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697234361, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697234583, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697234806, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697235012, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697235215, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697235523, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697235745, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697236336, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697236754, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697237219, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697237498, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697238069, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697238315, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697238373, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697238636, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697238693, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697238971, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697240181, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697240753, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697240922, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697241589, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697241949, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697242010, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697242181, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697242405, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697242921, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697243102, "dur": 1800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697244903, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751449697245060, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697245399, "dur": 2714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697248113, "dur": 36032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697284146, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697286461, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697286790, "dur": 3012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697289838, "dur": 3034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697292906, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697295749, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697295986, "dur": 2858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697298868, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751449697299132, "dur": 346976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751449697646130, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751449697646112, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751449697646272, "dur": 1545, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751449697647824, "dur": 75077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697201805, "dur": 23976, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697225782, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751449697226166, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C11B61CB0C885270.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751449697227157, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751449697227559, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751449697227817, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697227911, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751449697228360, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697228575, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697229260, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697230221, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697230469, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697230923, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697231137, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697231333, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697231549, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697231748, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697231959, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697232160, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697232439, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\RenderPipelineResources\\UniversalRendererResources.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751449697232361, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697233099, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697233308, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697233512, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697233717, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697233948, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697234253, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsObjectAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751449697234154, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697234892, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697235110, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697235323, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697235700, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697235976, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697236750, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697237210, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751449697237413, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697238015, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751449697238205, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697240894, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751449697241703, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697242145, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697242830, "dur": 2080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697244910, "dur": 3209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697248119, "dur": 34077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697282198, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697284514, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697287128, "dur": 2757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697289921, "dur": 2737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697292660, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697293311, "dur": 5582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751449697299111, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751449697299413, "dur": 423471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697201686, "dur": 24085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697225773, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697226051, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_05122CAABA8E3B59.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697226300, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697226353, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697226496, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E496E41FECA98925.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697227084, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751449697227187, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751449697227494, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751449697227777, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697228335, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697228537, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697228719, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697229440, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697229632, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697229821, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697230014, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697230234, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697230434, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697230634, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697231549, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697231747, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697231951, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697232575, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697232774, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697232997, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697233206, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697233409, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697233629, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697233820, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697234028, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697234236, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697234458, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697234664, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697234880, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697235085, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697235302, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697235536, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697235741, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697236373, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697236771, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697237205, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697237403, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697238262, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697238499, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697238909, "dur": 2181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697241143, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697241305, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697241866, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697241988, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697242178, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697242340, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697242695, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697243172, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697243270, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697243430, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697244201, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697244356, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697244900, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697245053, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697245616, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751449697245755, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697246299, "dur": 1810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697248109, "dur": 34091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697282201, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697284406, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697284625, "dur": 2287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697286952, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697289315, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697291680, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697293984, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751449697294041, "dur": 2327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697296397, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751449697299212, "dur": 423660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751449697751240, "dur": 1130, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1380, "tid": 270, "ts": 1751449697768855, "dur": 2496, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1380, "tid": 270, "ts": 1751449697771489, "dur": 1712, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1380, "tid": 270, "ts": 1751449697760515, "dur": 13525, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}