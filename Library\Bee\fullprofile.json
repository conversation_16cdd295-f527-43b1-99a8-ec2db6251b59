{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1380, "tid": 314, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1380, "tid": 314, "ts": 1751450057818480, "dur": 466, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1380, "tid": 314, "ts": 1751450057821351, "dur": 1075, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1380, "tid": 1, "ts": 1751450057161188, "dur": 6960, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1380, "tid": 1, "ts": 1751450057168163, "dur": 122275, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1380, "tid": 1, "ts": 1751450057290459, "dur": 128610, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1380, "tid": 314, "ts": 1751450057822433, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 1380, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057158670, "dur": 7006, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057165680, "dur": 642746, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057166612, "dur": 3366, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057169989, "dur": 2396, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057172394, "dur": 366, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057172767, "dur": 36, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057172805, "dur": 153, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057172965, "dur": 4, "ph": "X", "name": "ProcessMessages 1720", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057172971, "dur": 127, "ph": "X", "name": "ReadAsync 1720", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173110, "dur": 4, "ph": "X", "name": "ProcessMessages 1344", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173116, "dur": 136, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173259, "dur": 3, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173264, "dur": 145, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173416, "dur": 9, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173429, "dur": 140, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173575, "dur": 3, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173580, "dur": 127, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173715, "dur": 4, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173734, "dur": 121, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173861, "dur": 5, "ph": "X", "name": "ProcessMessages 2112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173869, "dur": 37, "ph": "X", "name": "ReadAsync 2112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173908, "dur": 2, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057173912, "dur": 121, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174039, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174043, "dur": 451, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174511, "dur": 3, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174516, "dur": 162, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174688, "dur": 13, "ph": "X", "name": "ProcessMessages 9403", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174706, "dur": 31, "ph": "X", "name": "ReadAsync 9403", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174738, "dur": 2, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174742, "dur": 105, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174854, "dur": 79, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174944, "dur": 4, "ph": "X", "name": "ProcessMessages 1228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174950, "dur": 42, "ph": "X", "name": "ReadAsync 1228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057174996, "dur": 11, "ph": "X", "name": "ProcessMessages 2050", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175009, "dur": 101, "ph": "X", "name": "ReadAsync 2050", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175114, "dur": 34, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175162, "dur": 4, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175186, "dur": 20, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175210, "dur": 20, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175232, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175235, "dur": 27, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175269, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175272, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175288, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175290, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175309, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175332, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175335, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175354, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175357, "dur": 17, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175378, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175382, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175399, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175402, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175420, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175423, "dur": 23, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175452, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175455, "dur": 22, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175479, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175482, "dur": 63, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175551, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175555, "dur": 23, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175583, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175587, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175606, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175608, "dur": 43, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175656, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175679, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175682, "dur": 17, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175701, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175703, "dur": 23, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175733, "dur": 3, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175739, "dur": 23, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175764, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175766, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175786, "dur": 11, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175799, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175823, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175826, "dur": 59, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175888, "dur": 1, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175890, "dur": 19, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175914, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175917, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175946, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175950, "dur": 18, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175970, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175973, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057175997, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176000, "dur": 35, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176038, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176041, "dur": 31, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176075, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176078, "dur": 15, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176097, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176118, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176120, "dur": 19, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176141, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176146, "dur": 23, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176171, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176174, "dur": 18, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176194, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176197, "dur": 15, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176215, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176218, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176239, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176242, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176265, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176268, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176290, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176293, "dur": 16, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176311, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176313, "dur": 139, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176456, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176479, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176482, "dur": 21, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176505, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176507, "dur": 26, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176535, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176538, "dur": 16, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176557, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176559, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176583, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176585, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176605, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176607, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176627, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176630, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176649, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176651, "dur": 16, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176670, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176672, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176688, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176691, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176716, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176718, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176741, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176744, "dur": 20, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176766, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176769, "dur": 23, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176793, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176796, "dur": 17, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176817, "dur": 3, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176822, "dur": 15, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176838, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176840, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176859, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176861, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176883, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176885, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176907, "dur": 4, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176913, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176933, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176936, "dur": 16, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176955, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176957, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176980, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057176983, "dur": 18, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177003, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177006, "dur": 18, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177026, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177029, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177049, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177051, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177077, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177100, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177104, "dur": 20, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177126, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177129, "dur": 19, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177150, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177153, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177174, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177177, "dur": 14, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177195, "dur": 22, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177218, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177221, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177243, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177245, "dur": 21, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177269, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177272, "dur": 19, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177294, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177297, "dur": 15, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177315, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177317, "dur": 20, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177340, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177343, "dur": 14, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177359, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177361, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177381, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177383, "dur": 37, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177425, "dur": 3, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177430, "dur": 37, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177470, "dur": 2, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177473, "dur": 17, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177492, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177495, "dur": 63, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177564, "dur": 3, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177569, "dur": 37, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177608, "dur": 3, "ph": "X", "name": "ProcessMessages 2117", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177612, "dur": 28, "ph": "X", "name": "ReadAsync 2117", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177643, "dur": 2, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177646, "dur": 16, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177667, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177670, "dur": 73, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177749, "dur": 4, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177755, "dur": 38, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177795, "dur": 3, "ph": "X", "name": "ProcessMessages 2397", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177801, "dur": 20, "ph": "X", "name": "ReadAsync 2397", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177823, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177826, "dur": 63, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177897, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177902, "dur": 33, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177937, "dur": 2, "ph": "X", "name": "ProcessMessages 1665", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177941, "dur": 22, "ph": "X", "name": "ReadAsync 1665", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177965, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057177967, "dur": 70, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178047, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178051, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178096, "dur": 4, "ph": "X", "name": "ProcessMessages 2470", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178101, "dur": 23, "ph": "X", "name": "ReadAsync 2470", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178127, "dur": 2, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178130, "dur": 25, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178158, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178163, "dur": 21, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178187, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178190, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178212, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178215, "dur": 23, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178241, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178244, "dur": 20, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178265, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178268, "dur": 62, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178338, "dur": 3, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178342, "dur": 38, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178382, "dur": 5, "ph": "X", "name": "ProcessMessages 1982", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178391, "dur": 23, "ph": "X", "name": "ReadAsync 1982", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178417, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178420, "dur": 67, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178493, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178499, "dur": 34, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178538, "dur": 2, "ph": "X", "name": "ProcessMessages 1407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178542, "dur": 22, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178567, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178571, "dur": 31, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178605, "dur": 2, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057178610, "dur": 467, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179090, "dur": 12, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179106, "dur": 107, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179215, "dur": 6, "ph": "X", "name": "ProcessMessages 5120", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179320, "dur": 193, "ph": "X", "name": "ReadAsync 5120", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179517, "dur": 268, "ph": "X", "name": "ProcessMessages 9293", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179788, "dur": 125, "ph": "X", "name": "ReadAsync 9293", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179918, "dur": 11, "ph": "X", "name": "ProcessMessages 8204", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179931, "dur": 30, "ph": "X", "name": "ReadAsync 8204", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179963, "dur": 5, "ph": "X", "name": "ProcessMessages 1604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057179969, "dur": 34, "ph": "X", "name": "ReadAsync 1604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180007, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180011, "dur": 24, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180037, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180044, "dur": 23, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180069, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180072, "dur": 19, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180096, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180101, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180133, "dur": 2, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180137, "dur": 62, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180205, "dur": 3, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180210, "dur": 37, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180253, "dur": 3, "ph": "X", "name": "ProcessMessages 2176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180257, "dur": 13, "ph": "X", "name": "ReadAsync 2176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180272, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180274, "dur": 28, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180304, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180307, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180333, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180336, "dur": 30, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180370, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180374, "dur": 20, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180396, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180399, "dur": 27, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180428, "dur": 2, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180435, "dur": 28, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180466, "dur": 2, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180469, "dur": 17, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180487, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180490, "dur": 14, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180509, "dur": 19, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180530, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180532, "dur": 16, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180550, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180555, "dur": 16, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180573, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180576, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180598, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180601, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180624, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180627, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180650, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180653, "dur": 59, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180719, "dur": 3, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180724, "dur": 37, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180764, "dur": 2, "ph": "X", "name": "ProcessMessages 1586", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180768, "dur": 22, "ph": "X", "name": "ReadAsync 1586", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180792, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180795, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180817, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180821, "dur": 55, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180882, "dur": 3, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180887, "dur": 37, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180926, "dur": 2, "ph": "X", "name": "ProcessMessages 1622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180930, "dur": 18, "ph": "X", "name": "ReadAsync 1622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180950, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180953, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180971, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057180974, "dur": 66, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181046, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181051, "dur": 35, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181088, "dur": 4, "ph": "X", "name": "ProcessMessages 2118", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181094, "dur": 34, "ph": "X", "name": "ReadAsync 2118", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181130, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181134, "dur": 22, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181159, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181161, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181184, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181187, "dur": 18, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181207, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181209, "dur": 22, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181235, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181237, "dur": 29, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181269, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181272, "dur": 25, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181299, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181302, "dur": 21, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181326, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181329, "dur": 22, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181354, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181357, "dur": 63, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181426, "dur": 3, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181431, "dur": 35, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181468, "dur": 2, "ph": "X", "name": "ProcessMessages 1701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181474, "dur": 19, "ph": "X", "name": "ReadAsync 1701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181495, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181497, "dur": 19, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181519, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181522, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181541, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181544, "dur": 17, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181563, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181565, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181591, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181594, "dur": 19, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181615, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181617, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181643, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181646, "dur": 21, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181669, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181672, "dur": 67, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181745, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181750, "dur": 37, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181789, "dur": 3, "ph": "X", "name": "ProcessMessages 2129", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181793, "dur": 16, "ph": "X", "name": "ReadAsync 2129", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181816, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181819, "dur": 33, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181854, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181856, "dur": 20, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181880, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181882, "dur": 26, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181910, "dur": 2, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181913, "dur": 17, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181935, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181938, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181961, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181965, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181989, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057181992, "dur": 66, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182067, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182072, "dur": 38, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182112, "dur": 8, "ph": "X", "name": "ProcessMessages 1798", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182122, "dur": 16, "ph": "X", "name": "ReadAsync 1798", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182140, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182143, "dur": 16, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182163, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182186, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182189, "dur": 18, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182209, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182211, "dur": 58, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182276, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182281, "dur": 42, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182325, "dur": 3, "ph": "X", "name": "ProcessMessages 2289", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182331, "dur": 21, "ph": "X", "name": "ReadAsync 2289", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182354, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182357, "dur": 15, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182374, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182377, "dur": 19, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182400, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182403, "dur": 22, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182427, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182430, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182451, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182454, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182474, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182476, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182494, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182496, "dur": 62, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182564, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182570, "dur": 44, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182616, "dur": 3, "ph": "X", "name": "ProcessMessages 1845", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182620, "dur": 30, "ph": "X", "name": "ReadAsync 1845", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182653, "dur": 2, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182657, "dur": 17, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182676, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182679, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182702, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182705, "dur": 56, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182767, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182772, "dur": 41, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182817, "dur": 3, "ph": "X", "name": "ProcessMessages 1792", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182822, "dur": 13, "ph": "X", "name": "ReadAsync 1792", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182837, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182840, "dur": 14, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182859, "dur": 12, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182873, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182875, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182909, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182935, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057182938, "dur": 64, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183008, "dur": 3, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183013, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183037, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183039, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183061, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183083, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183086, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183110, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183113, "dur": 23, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183138, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183141, "dur": 16, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183159, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183162, "dur": 23, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183190, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183193, "dur": 78, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183277, "dur": 3, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183282, "dur": 36, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183321, "dur": 2, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183324, "dur": 19, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183345, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183348, "dur": 12, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183364, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183384, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183386, "dur": 34, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183422, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183426, "dur": 15, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183444, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183447, "dur": 22, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183471, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183474, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183522, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183546, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183548, "dur": 23, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183574, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183577, "dur": 53, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183637, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183671, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183675, "dur": 17, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183694, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183697, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183749, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183772, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183774, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183799, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183802, "dur": 51, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183857, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183879, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183881, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183904, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183906, "dur": 51, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183961, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183985, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057183987, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184008, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184010, "dur": 15, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184027, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184029, "dur": 49, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184082, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184107, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184110, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184131, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184135, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184155, "dur": 3, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184159, "dur": 45, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184209, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184232, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184235, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184259, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184262, "dur": 60, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184326, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184360, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184363, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184387, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184390, "dur": 78, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184473, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184501, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184504, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184531, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184534, "dur": 52, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184589, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184617, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184621, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184639, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184641, "dur": 64, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184710, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184733, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184736, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184761, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184764, "dur": 53, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184822, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184845, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184848, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184873, "dur": 3, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184878, "dur": 14, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184894, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184897, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184941, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057184943, "dur": 128, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185075, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185079, "dur": 30, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185111, "dur": 2, "ph": "X", "name": "ProcessMessages 1822", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185115, "dur": 35, "ph": "X", "name": "ReadAsync 1822", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185154, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185177, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185180, "dur": 34, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185219, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185221, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185244, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185248, "dur": 30, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185281, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185303, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185306, "dur": 21, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185329, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185332, "dur": 52, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185388, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185410, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185413, "dur": 20, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185435, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185437, "dur": 14, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185453, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185455, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185505, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185529, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185532, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185554, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185556, "dur": 51, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185614, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185636, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185638, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185659, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185662, "dur": 14, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185678, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185681, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185730, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185752, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185755, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185783, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185786, "dur": 44, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185834, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185856, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185858, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185877, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185879, "dur": 13, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185894, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185897, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185949, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185971, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057185974, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186000, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186003, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186024, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186027, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186049, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186052, "dur": 17, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186070, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186073, "dur": 19, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186094, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186096, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186152, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186181, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186183, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186206, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186209, "dur": 48, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186261, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186282, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186285, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186306, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186309, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186331, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186334, "dur": 20, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186356, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186358, "dur": 21, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186382, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186385, "dur": 25, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186412, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186415, "dur": 55, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186474, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186498, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186501, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186523, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186525, "dur": 15, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186543, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186545, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186595, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186616, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186619, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186640, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186643, "dur": 16, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186661, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186663, "dur": 43, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186710, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186738, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186740, "dur": 26, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186769, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186773, "dur": 44, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186820, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186842, "dur": 4, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186848, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186870, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186872, "dur": 25, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186900, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186903, "dur": 48, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186955, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186977, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057186979, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187006, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187009, "dur": 20, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187031, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187034, "dur": 28, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187064, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187067, "dur": 15, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187085, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187087, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187107, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187109, "dur": 46, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187160, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187185, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187187, "dur": 25, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187215, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187217, "dur": 16, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187236, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187238, "dur": 43, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187285, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187311, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187314, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187336, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187339, "dur": 19, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187359, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187362, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187382, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187385, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187407, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187410, "dur": 14, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187426, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187428, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187446, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187449, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187498, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187519, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187522, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187547, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187550, "dur": 49, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187603, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187625, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187628, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187647, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187650, "dur": 24, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187676, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187678, "dur": 18, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187699, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187701, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187726, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187729, "dur": 15, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187746, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187749, "dur": 18, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187769, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187771, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187823, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187845, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187848, "dur": 23, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187874, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187876, "dur": 16, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187894, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187897, "dur": 40, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187942, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187966, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187969, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187992, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057187994, "dur": 14, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188011, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188013, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188059, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188081, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188083, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188102, "dur": 3, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188107, "dur": 17, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188236, "dur": 45, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188284, "dur": 4, "ph": "X", "name": "ProcessMessages 1720", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188439, "dur": 68, "ph": "X", "name": "ReadAsync 1720", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188513, "dur": 4, "ph": "X", "name": "ProcessMessages 1198", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188520, "dur": 22, "ph": "X", "name": "ReadAsync 1198", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188543, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188546, "dur": 140, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188691, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188725, "dur": 2, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188729, "dur": 18, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188750, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188752, "dur": 59, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188815, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188847, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188851, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188887, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188891, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188920, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188945, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188949, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188969, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057188972, "dur": 53, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189030, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189069, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189074, "dur": 16, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189091, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189094, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189140, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189142, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189161, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189164, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189191, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189193, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189214, "dur": 4, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189220, "dur": 23, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189245, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189248, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189271, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189277, "dur": 18, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189297, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189299, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189360, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189383, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189386, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189408, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189410, "dur": 14, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189427, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189429, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189475, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189497, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189500, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189523, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189526, "dur": 47, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189578, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189600, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189603, "dur": 22, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189627, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189629, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189652, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189655, "dur": 49, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189708, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189737, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189740, "dur": 16, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189757, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189760, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189782, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189801, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189804, "dur": 45, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189853, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189875, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189877, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189901, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189904, "dur": 50, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189957, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189959, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189983, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057189986, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190008, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190010, "dur": 47, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190061, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190081, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190084, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190106, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190109, "dur": 52, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190165, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190188, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190191, "dur": 28, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190221, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190224, "dur": 19, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190250, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190253, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190280, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190282, "dur": 15, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190300, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190303, "dur": 16, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190321, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190325, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190378, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190400, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190403, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190426, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190428, "dur": 14, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190445, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190448, "dur": 41, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190494, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190517, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190520, "dur": 19, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190541, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190543, "dur": 15, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190561, "dur": 1, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190564, "dur": 49, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190616, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190640, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190643, "dur": 24, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190669, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190672, "dur": 46, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190723, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190745, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190748, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190770, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190773, "dur": 47, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190824, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190846, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190849, "dur": 21, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190872, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190875, "dur": 48, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190927, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190949, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190952, "dur": 22, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190977, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057190980, "dur": 47, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191031, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191052, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191055, "dur": 23, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191080, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191083, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191101, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191104, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191167, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191191, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191194, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191217, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191220, "dur": 17, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191239, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191242, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191287, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191308, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191311, "dur": 20, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191333, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191336, "dur": 47, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191391, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191412, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191415, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191436, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191439, "dur": 15, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191456, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191458, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191505, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191527, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191530, "dur": 19, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191551, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191553, "dur": 47, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191604, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191625, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191628, "dur": 18, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191647, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191650, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191676, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191678, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191722, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191745, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191748, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191772, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191775, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191795, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191797, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191820, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191823, "dur": 22, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191848, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191850, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191869, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191871, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191888, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191892, "dur": 41, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191937, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191960, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191963, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191983, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191985, "dur": 11, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057191998, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192000, "dur": 48, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192052, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192074, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192076, "dur": 20, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192098, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192101, "dur": 48, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192153, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192174, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192176, "dur": 21, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192199, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192202, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192222, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192224, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192269, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192292, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192295, "dur": 19, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192316, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192319, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192336, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192338, "dur": 45, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192387, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192409, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192412, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192434, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192436, "dur": 102, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192540, "dur": 3, "ph": "X", "name": "ProcessMessages 1633", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192544, "dur": 33, "ph": "X", "name": "ReadAsync 1633", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192580, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192602, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192605, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192623, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192625, "dur": 12, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192640, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192642, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192698, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192722, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192725, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192746, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192749, "dur": 49, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192803, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192825, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192828, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192857, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192860, "dur": 47, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192910, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192934, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192937, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192958, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192961, "dur": 35, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057192998, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193000, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193031, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193053, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193055, "dur": 20, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193077, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193080, "dur": 50, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193134, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193154, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193157, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193179, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193181, "dur": 14, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193198, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193201, "dur": 40, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193245, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193265, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193268, "dur": 32, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193302, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193305, "dur": 46, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193356, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193377, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193380, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193403, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193405, "dur": 15, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193422, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193425, "dur": 49, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193480, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193502, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193505, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193525, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193527, "dur": 18, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193547, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193551, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193573, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193576, "dur": 23, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193601, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193604, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193623, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193626, "dur": 16, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193644, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193646, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193699, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193721, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193724, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193746, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193748, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193770, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193772, "dur": 40, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193817, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193838, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193841, "dur": 22, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193865, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193867, "dur": 21, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193890, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193893, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193914, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193917, "dur": 19, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193939, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193941, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193960, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057193963, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194018, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194040, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194043, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194068, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194070, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194093, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194096, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194114, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194116, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194137, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194140, "dur": 13, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194154, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194156, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194181, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194183, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194231, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194251, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194255, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194277, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194279, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194300, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194303, "dur": 20, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194325, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194328, "dur": 19, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194349, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194352, "dur": 17, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194371, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194374, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194392, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194394, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194449, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194468, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194472, "dur": 83, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194562, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194567, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057194607, "dur": 439, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195051, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195112, "dur": 10, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195124, "dur": 29, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195159, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195167, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195195, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195201, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195230, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195236, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195259, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195264, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195295, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195301, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195343, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195348, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195381, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195386, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195423, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195427, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195462, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195469, "dur": 22, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195495, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195499, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195525, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195531, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195567, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195573, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195605, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195612, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195648, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195654, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195699, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195707, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195745, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195752, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195791, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195798, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195827, "dur": 4, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195834, "dur": 33, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195871, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195878, "dur": 28, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195911, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057195919, "dur": 123, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196047, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196055, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196104, "dur": 9, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196115, "dur": 51, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196171, "dur": 6, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196180, "dur": 35, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196220, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196227, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196265, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196271, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196308, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196315, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196351, "dur": 5, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196361, "dur": 33, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196398, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196404, "dur": 33, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196442, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196451, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196492, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196500, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196526, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196531, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196559, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196564, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196603, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196612, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196647, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196652, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196677, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196679, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196743, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196769, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196774, "dur": 18, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196795, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196799, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196830, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196836, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196869, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196876, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196908, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196914, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196949, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196955, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196988, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057196994, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197031, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197037, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197067, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197072, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197101, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197107, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197131, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197136, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197169, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197177, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197214, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197220, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197250, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197255, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197292, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197299, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197330, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197335, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197364, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197368, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197390, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197393, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197429, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197449, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057197452, "dur": 8472, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057205939, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057205948, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206015, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206021, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206053, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206059, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206169, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206173, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206211, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206214, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206853, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206858, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206886, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206891, "dur": 71, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206971, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057206976, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207005, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207009, "dur": 324, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207341, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207346, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207380, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207386, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207505, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207539, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207543, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207572, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207576, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207612, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207638, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207642, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207697, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207716, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207720, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207758, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207763, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207787, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207793, "dur": 197, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057207997, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208019, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208022, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208112, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208129, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208132, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208319, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208321, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208358, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208363, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208386, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208390, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208447, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208470, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208474, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208504, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208521, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208524, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208554, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208557, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208581, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208586, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208607, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208624, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208627, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208642, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208645, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208667, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208671, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208785, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208789, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208824, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208829, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208860, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208864, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208883, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208887, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208944, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208949, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208969, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208973, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208992, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057208995, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209093, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209122, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209126, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209156, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209160, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209180, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209182, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209206, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209227, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209231, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209306, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209312, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209335, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209338, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209354, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209357, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209464, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209492, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209498, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209529, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209549, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209553, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209573, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209576, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209622, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209647, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209650, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209680, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209683, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209707, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209710, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209729, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209732, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209755, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209777, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209780, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209827, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209840, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209860, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209864, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209883, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209902, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057209906, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210004, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210026, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210030, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210050, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210053, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210085, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210088, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210109, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210113, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210132, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210239, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210266, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210383, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210401, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210404, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210451, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210472, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210475, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210544, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210548, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210579, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210583, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210605, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210608, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210635, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210639, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210665, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210669, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210690, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210694, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210718, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210721, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210747, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210750, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210823, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210844, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210908, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210939, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210960, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210963, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210988, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057210992, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211027, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211053, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211071, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211074, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211093, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211097, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211120, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211125, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211140, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211143, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211164, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211168, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211190, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211193, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211244, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211275, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211279, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211303, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211334, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211353, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211357, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211405, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211422, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211426, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211468, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211493, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211498, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211577, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211604, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211608, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211640, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211645, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211679, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211698, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211700, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211725, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211750, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211754, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211797, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211816, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211818, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211849, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211854, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211879, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211902, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057211908, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212053, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212055, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212072, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212075, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212093, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212096, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212120, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212124, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212148, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212151, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212177, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212181, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212231, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212252, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212255, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212282, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212286, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212311, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212329, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212332, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212353, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212356, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212374, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212377, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212404, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212422, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212443, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212448, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212481, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212486, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212519, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212524, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212547, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212551, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212589, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212608, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212613, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212706, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212710, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212736, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212740, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212766, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212790, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212795, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212852, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212874, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212881, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212904, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057212908, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213016, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213020, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213042, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213048, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213301, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213305, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213325, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213329, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213357, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213360, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213392, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213398, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213421, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213424, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213484, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213488, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057213514, "dur": 903, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214427, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214492, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214502, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214548, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214553, "dur": 225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214788, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214828, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214832, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214863, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214872, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214928, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057214932, "dur": 93, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215038, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215042, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215077, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215081, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215109, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215111, "dur": 311, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215428, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215433, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215469, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215474, "dur": 161, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215642, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215650, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215690, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215694, "dur": 101, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215802, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215806, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215830, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057215833, "dur": 170, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216009, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216013, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216039, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216126, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216162, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216168, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216216, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216220, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216389, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216393, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216495, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216499, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216534, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216537, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216573, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216579, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216598, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216600, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216633, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216647, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216650, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216689, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216693, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216724, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216746, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216748, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216895, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216898, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216979, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057216982, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217056, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217059, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217131, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217135, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217183, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217200, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217202, "dur": 554, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217763, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217767, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217791, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217794, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217915, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057217924, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218043, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218047, "dur": 447, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218499, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218520, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218522, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218573, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218578, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218597, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218600, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218617, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218620, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057218904, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219015, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219019, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219107, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219113, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219205, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219232, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219236, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219402, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219406, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219436, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057219439, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057220082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057220086, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057220108, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057220114, "dur": 30321, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057250448, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057250455, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057250519, "dur": 2082, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057252610, "dur": 6693, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259320, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259329, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259403, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259409, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259452, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259458, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057259493, "dur": 770, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260271, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260276, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260299, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260302, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260377, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260400, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057260403, "dur": 1194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261605, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261609, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261629, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261666, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261703, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261709, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261744, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261750, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261853, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261858, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261897, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261902, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261938, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261943, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261968, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057261972, "dur": 887, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057262865, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057262869, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057262894, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057262898, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057262983, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057262986, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263006, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263009, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263039, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263044, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263067, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263070, "dur": 770, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263849, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263865, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263872, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263930, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263953, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057263957, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264036, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264039, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264060, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264063, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264164, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264195, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264201, "dur": 388, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264594, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264600, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264634, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057264638, "dur": 685, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265329, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265333, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265362, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265366, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265420, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265446, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265450, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265624, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265628, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265655, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265659, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265759, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265776, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057265779, "dur": 522, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266310, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266336, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266340, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266363, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266367, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266421, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266438, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266441, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266454, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266457, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266549, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266577, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266581, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266604, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266989, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057266993, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267015, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267018, "dur": 276, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267301, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267331, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267713, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267717, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267752, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267758, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267835, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267860, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267864, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267968, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057267998, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268001, "dur": 486, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268492, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268501, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268528, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268532, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268557, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268562, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268687, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268691, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268713, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268716, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268830, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268859, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268864, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057268892, "dur": 433, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269329, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269332, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269356, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269359, "dur": 383, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269751, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269777, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057269780, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270095, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270098, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270118, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270121, "dur": 446, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270577, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270608, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270613, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270722, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270779, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270784, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270811, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270891, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270897, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270927, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057270930, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271058, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271083, "dur": 538, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271627, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271632, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271658, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271661, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271703, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271724, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057271727, "dur": 542, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272275, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272279, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272308, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272312, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272331, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272335, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272367, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272372, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272404, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272408, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272446, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272451, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272483, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272488, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272508, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272512, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272564, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272568, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272598, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272602, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272621, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272624, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272651, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272655, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272678, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272683, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272702, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272706, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272720, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272723, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272750, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272754, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272777, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272781, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272795, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272797, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272827, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272832, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272853, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272862, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272890, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272894, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272911, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272914, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272939, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272955, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272958, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272977, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272981, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057272995, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273000, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273177, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273193, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273196, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273212, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273215, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273245, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273252, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273309, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273315, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273337, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273340, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273363, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273367, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273385, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273389, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273408, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273412, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273435, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273438, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273465, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273470, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273492, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273495, "dur": 14, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273512, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273515, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273540, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273545, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273574, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273579, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273596, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273600, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273621, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273626, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273646, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273649, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273664, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273667, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273684, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273688, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273703, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273706, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273728, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273732, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273751, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273755, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273781, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273789, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273819, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273825, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273851, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273855, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273878, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273883, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273906, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273910, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273934, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273939, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273966, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057273969, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274004, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274027, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274031, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274050, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274055, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274079, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274084, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274118, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274124, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274151, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274157, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274210, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274218, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274255, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274258, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274353, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274358, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274393, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274402, "dur": 109, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274526, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274530, "dur": 133, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274669, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274674, "dur": 199, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274879, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057274883, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057275015, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057275023, "dur": 209486, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057484522, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057484529, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057484559, "dur": 37, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057484598, "dur": 19905, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057504515, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057504521, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057504656, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057504660, "dur": 78037, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582712, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582718, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582757, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582763, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582806, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582815, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582869, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057582876, "dur": 89927, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057672817, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057672823, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057672858, "dur": 26, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057672886, "dur": 14657, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057687557, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057687564, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057687593, "dur": 43, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057687639, "dur": 2178, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057689827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057689832, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057689951, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057689956, "dur": 13054, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057703022, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057703029, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057703168, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057703175, "dur": 1554, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057704735, "dur": 14, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057704752, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057704876, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057704903, "dur": 69375, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057774291, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057774297, "dur": 162, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057774466, "dur": 14, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057774482, "dur": 1339, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057775830, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057775835, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057775894, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057775924, "dur": 1144, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057777075, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057777078, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057777209, "dur": 567, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 1380, "tid": 12884901888, "ts": 1751450057777783, "dur": 30393, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 1380, "tid": 314, "ts": 1751450057822458, "dur": 6497, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1380, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1380, "tid": 8589934592, "ts": 1751450057156249, "dur": 262892, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1380, "tid": 8589934592, "ts": 1751450057419144, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1380, "tid": 8589934592, "ts": 1751450057419155, "dur": 1012, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1380, "tid": 314, "ts": 1751450057828960, "dur": 22, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1380, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450057136029, "dur": 673522, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450057141187, "dur": 7533, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450057809811, "dur": 5021, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450057812486, "dur": 150, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1380, "tid": 4294967296, "ts": 1751450057814934, "dur": 22, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1380, "tid": 314, "ts": 1751450057828985, "dur": 28, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751450057162467, "dur": 1948, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057164432, "dur": 1984, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057166527, "dur": 144, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751450057166671, "dur": 410, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057168086, "dur": 894, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057169727, "dur": 2942, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450057172790, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_1236C9FFD38EBE96.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057173094, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_98027424E2154AC3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057173243, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450057173413, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751450057173732, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_AEA6B4BCAB1E95DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057173849, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751450057174021, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_D6B2A7AC1E309D0B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057174492, "dur": 151, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450057174690, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751450057174937, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450057175092, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450057175433, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057175773, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751450057179087, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751450057179321, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751450057179771, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751450057188263, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751450057167098, "dur": 27321, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057194437, "dur": 581376, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057775815, "dur": 961, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057776949, "dur": 24886, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751450057167292, "dur": 27151, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057194519, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057194691, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057194773, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B10B806206A4C9C0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057194900, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057195036, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057195153, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_BA31E0AD397170B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057195785, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751450057196050, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751450057196259, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751450057196346, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751450057196555, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751450057196630, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057197223, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057197420, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057197617, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057198683, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057198891, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057199087, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057199292, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057199560, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057199782, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057200363, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057200577, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057200799, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057201489, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057201710, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057202024, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Invocations\\InvocationInspector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751450057201917, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057202888, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057203127, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057203337, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057203537, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057204038, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057204239, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057204447, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057204649, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057204852, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057205054, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057205278, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057205502, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057205960, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057206793, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057207434, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057207715, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057208543, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057208832, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_09A2B486E6034DFE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057208889, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057209169, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057209321, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057209420, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057209639, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057209821, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057209979, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057210668, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057210934, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057211263, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057212300, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057212507, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057212812, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057212971, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057213618, "dur": 1967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057215586, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057215751, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057216532, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751450057216688, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057217157, "dur": 1692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057218850, "dur": 38285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057257136, "dur": 4613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057261750, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057261810, "dur": 2559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057264370, "dur": 906, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057265283, "dur": 2605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057267921, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057270416, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057271013, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751450057273724, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057273920, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751450057274116, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751450057274526, "dur": 501246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057167400, "dur": 27053, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057194515, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450057194766, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_424FA579C047A49A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450057194904, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_502EE2D33971F102.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450057195564, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450057195743, "dur": 10051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057205941, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057206819, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057207519, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751450057208429, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057209219, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057209490, "dur": 1665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057211155, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057211828, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057212546, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057213829, "dur": 1772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057215602, "dur": 3253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057218856, "dur": 40435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057259293, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057261686, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057264032, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057264121, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057266318, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057266380, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057268450, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057270736, "dur": 3420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751450057274219, "dur": 415459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751450057689698, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751450057689679, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751450057689778, "dur": 86010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057167465, "dur": 26993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057194459, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450057194721, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450057195827, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751450057196116, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751450057196304, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751450057196556, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057196782, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751450057197131, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14092230165525918288.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751450057197266, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057197499, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057198500, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057199295, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057199532, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057199756, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057200237, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057200521, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057200733, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057200951, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057201170, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057201393, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057201582, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057201790, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057202006, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057202237, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057202452, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057202664, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057202871, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057203160, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057203369, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057203587, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057203807, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057204034, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057204246, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057204459, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057204667, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057204873, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057205071, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057205269, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057205487, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057206308, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057206806, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057207305, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450057207491, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057208800, "dur": 725, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057209535, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057209606, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450057209784, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057210337, "dur": 1204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057211541, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057211594, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057211688, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450057212115, "dur": 1167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057213330, "dur": 2278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057215608, "dur": 3239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057218849, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751450057219116, "dur": 38027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057257145, "dur": 2201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057259387, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057261609, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057261672, "dur": 2181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057263893, "dur": 2352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057266245, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057266414, "dur": 2018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057268465, "dur": 2141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057270607, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057270677, "dur": 2784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057273498, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057273899, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751450057274073, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057274197, "dur": 214337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057488564, "dur": 91152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751450057488540, "dur": 92598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057582245, "dur": 212, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751450057582916, "dur": 104545, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751450057702677, "dur": 71426, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751450057702670, "dur": 71435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751450057774120, "dur": 1607, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751450057167526, "dur": 26940, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057194468, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057194878, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F4BBF2BF2203F2D5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057194978, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_32AC050EF3A70AF5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057195122, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D5FB1AB832332DD5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057195699, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751450057196026, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751450057196568, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057196733, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751450057196802, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751450057196899, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678363927977808685.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751450057197092, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2196223717492739224.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751450057197335, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057197571, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057198635, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057198917, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057199114, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057199322, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057199556, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057199779, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057200240, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057200447, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057200654, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057200850, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057201066, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057201270, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057201465, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057201657, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057201865, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057202095, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057202310, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057202534, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057202745, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057202956, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057203176, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057203379, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057203610, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057203809, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057204217, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057204408, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057204598, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057204800, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057205018, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057205202, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057205410, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057206307, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057206808, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057207303, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057207525, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057209391, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057209664, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057210161, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057210560, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057212055, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057212192, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057213044, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057213478, "dur": 2110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057215588, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057216536, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751450057216648, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057216988, "dur": 1884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057218872, "dur": 38274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057257149, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057259756, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057260224, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057262602, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057262947, "dur": 3972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057266951, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057269180, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751450057269285, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057271670, "dur": 2784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751450057274512, "dur": 501291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057167591, "dur": 26881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057194475, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057194700, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057194770, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057195047, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057195117, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B9FBC03C59DCE731.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057195782, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751450057196305, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751450057196439, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751450057196595, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057196783, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751450057197238, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057197465, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057198444, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057199165, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057199419, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057199643, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057199849, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057200331, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\UpgradeTools\\Utilities\\UpgradeLogWriter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751450057200320, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057201118, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057201321, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057201507, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057201697, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057201914, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057202149, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057202349, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057202569, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057202761, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057202965, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057203169, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057203374, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057203581, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057203804, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057204100, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections.PerformanceTests.Internal\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751450057204091, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057204907, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057205116, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057205314, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057205508, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057205690, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057205769, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057205939, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057206802, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057207305, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057207473, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057208120, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057208624, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057208805, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057209992, "dur": 1170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057211163, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057211640, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057212268, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057212479, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057212806, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057213277, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057213360, "dur": 2226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057215587, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751450057215708, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057216002, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057216094, "dur": 2766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057218860, "dur": 38299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057257163, "dur": 2039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057259202, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057259299, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057261412, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057261841, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057264014, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057266231, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057266505, "dur": 4124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057270632, "dur": 1018, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751450057271659, "dur": 2555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751450057274261, "dur": 501514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057167642, "dur": 26836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057194481, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057194819, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_5AE8CAD8193F1AE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057195040, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057195158, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_A0847C1FE8DF0FE2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057195748, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450057195866, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751450057196045, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450057196309, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751450057196604, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057196738, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751450057197227, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057197743, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057198819, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057199009, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057199206, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057199431, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057199646, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057199856, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057200328, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\SpriteLib\\SpriteSwapOverlay\\CategoryContainer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751450057200328, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057201145, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057201508, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\SamplerStateShaderProperty.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751450057201359, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057202078, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057202290, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057202500, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057202749, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057202964, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057203170, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057203379, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057203660, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057203921, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057204130, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057204341, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057204539, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057205147, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057205235, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057205440, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057206114, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057206802, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057207317, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057207567, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057208088, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057208274, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057208774, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057208846, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057209106, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057209299, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057209486, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057209680, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057210201, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057210673, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057210853, "dur": 994, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057211850, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057212618, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057212739, "dur": 1978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057214719, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751450057214860, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057215289, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057215389, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057215643, "dur": 3207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057218851, "dur": 38285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057257138, "dur": 1906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057259046, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057259226, "dur": 2420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057261680, "dur": 5411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057267094, "dur": 690, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057267792, "dur": 1865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057269657, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057270061, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751450057272364, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057272451, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057272872, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057273406, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057273693, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057273957, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751450057274049, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/spine-csharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751450057274122, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751450057274801, "dur": 501007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057167873, "dur": 26623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057194498, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057194645, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057194782, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057195125, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3CE377B30C500CB9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057195701, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_D4B653123EE78879.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057195827, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751450057195959, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751450057196116, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751450057196348, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751450057196555, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751450057196609, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057196741, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751450057197029, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14508719319121739504.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751450057197384, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057197580, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057198525, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057198863, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057199515, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057199948, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057200403, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057200625, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057200818, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057201039, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057201247, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057201442, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057201633, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057201845, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057202060, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057202269, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057202523, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Framework\\Control\\ForEach.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751450057202482, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057203261, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057203465, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057203667, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057203879, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057204152, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057204366, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057204569, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057204785, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057204985, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057205192, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057205773, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057205952, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057206794, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057207310, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057207486, "dur": 1670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057209157, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057209258, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_B73CFB0D33CD813E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057209489, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057209726, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057210346, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057210408, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057210984, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057211129, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_CAAF5CDDE611F3DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057211201, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057211363, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057211987, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057212073, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057212464, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057213069, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057213316, "dur": 2277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057215593, "dur": 2858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057218454, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751450057218572, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057218891, "dur": 40409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057259302, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057261517, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057261658, "dur": 3605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057265264, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057265378, "dur": 3062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057268441, "dur": 1253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057269700, "dur": 4068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751450057273805, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057273901, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751450057274032, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057274102, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057274228, "dur": 428474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751450057702723, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751450057702703, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751450057702878, "dur": 1670, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751450057704552, "dur": 71249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057167703, "dur": 26781, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057194486, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057194780, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057195001, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057195165, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_4DE7717F601CC2DC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057196045, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751450057196287, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751450057196553, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751450057196615, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057196789, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751450057197248, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057197531, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057198505, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057199250, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057199670, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057199868, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057200333, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057200548, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057200754, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057200997, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057201214, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057201415, "dur": 2474, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\GraphDataReadOnly.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751450057203899, "dur": 887, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\GraphData.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751450057204878, "dur": 766, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\CubemapMaterialSlot.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751450057201414, "dur": 4414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057205828, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057205940, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057206828, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057207517, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057207735, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057208574, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057208845, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057209483, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057209863, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057210062, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057210696, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751450057210939, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057211073, "dur": 1237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057212313, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057213049, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057213323, "dur": 2280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057215604, "dur": 3247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057218852, "dur": 38280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057257135, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057259585, "dur": 738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057260330, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057263004, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057265718, "dur": 2835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057268554, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751450057268826, "dur": 2749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057271616, "dur": 2674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751450057274357, "dur": 501416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057167941, "dur": 26560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057194504, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057194659, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057194776, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057194996, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057195656, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057195856, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751450057195968, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751450057196341, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751450057196437, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751450057196571, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751450057196630, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057196742, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751450057197132, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1929131816769834074.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751450057197299, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057197547, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057198485, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057199154, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057199386, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057199598, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057199807, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057200268, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057200474, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057200682, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057200875, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057201126, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057201325, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057201515, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057201715, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057201927, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057202129, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057202327, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057202541, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057202753, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057202950, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057203152, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057203352, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057203552, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057203772, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057203992, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057204207, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057204406, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057204611, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057204819, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057205031, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057205218, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057205473, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057206172, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057206832, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057207298, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057207483, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057208370, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057208571, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057208800, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057210197, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057210589, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057210869, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057211108, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057212184, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057212289, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057212473, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057212615, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057213377, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057213966, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057214126, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057214855, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057214993, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057215584, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057215766, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057216236, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057216326, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751450057216479, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057216863, "dur": 1993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057218856, "dur": 38311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057257170, "dur": 4558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057261729, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057261813, "dur": 4069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057265883, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057266314, "dur": 6648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751450057272963, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057273311, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057273484, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057273582, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751450057273855, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057274109, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751450057274339, "dur": 501431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057167771, "dur": 26719, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057194492, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057194652, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057194912, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A01C5835F2B3673A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057194980, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE214F9E6C036FF9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057195040, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057195110, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_634E261A11766DA3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057195862, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751450057195975, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751450057196109, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751450057196448, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751450057196590, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057197063, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13473629827552830373.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751450057197232, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057197428, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057198007, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057199138, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057199376, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057199620, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057199845, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057200393, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057200601, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057200803, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057201048, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057201251, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057201450, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057201678, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057201891, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057202108, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057202324, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057202533, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057202738, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057202941, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057203137, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057203342, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057203554, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057203766, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057204138, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057204352, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057204561, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057204766, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057204967, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057205156, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057205372, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057205975, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057206796, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057207310, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057207522, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057209067, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057209432, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057209672, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057210194, "dur": 1807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057212002, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057212099, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057212221, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057212393, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057212567, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057213783, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057213936, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057214449, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057215644, "dur": 3201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057218846, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751450057219099, "dur": 38040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057257141, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057259375, "dur": 2215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057261624, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057263888, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057266197, "dur": 1463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057267666, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057269938, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057270525, "dur": 3431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751450057274104, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751450057274269, "dur": 501500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057168081, "dur": 26427, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057194510, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057194763, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057195081, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057195536, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057195674, "dur": 10214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057205971, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057206116, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057206819, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057206919, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057207296, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057207455, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057208200, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057208338, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057208531, "dur": 1034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057209565, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057210073, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057210778, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057210946, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057211641, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057212223, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057212492, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751450057212682, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057213315, "dur": 2303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057215619, "dur": 3249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057218868, "dur": 38273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057257143, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057259317, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057259390, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057261585, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057261832, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057264027, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057264150, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057266468, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751450057266948, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057269287, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057271585, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751450057274409, "dur": 501359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057168452, "dur": 26080, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057194534, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057194769, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057195033, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057195705, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751450057196118, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751450057196286, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751450057196452, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751450057196621, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057196940, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4730270780318326332.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751450057197214, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16876957367423690161.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751450057197289, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057197516, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057198577, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057198760, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057198955, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057199139, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057199375, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057199628, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057199841, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057200302, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057200499, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057200708, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057200902, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057201105, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057201305, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057201497, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057201697, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057201900, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057202125, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057202319, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057202534, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057202743, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057202945, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057203147, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057203360, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057203571, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057203802, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057204217, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057204436, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057204728, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@001b54a8988a\\Editor\\UI\\Avatar\\GetAvatar.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751450057204636, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057205408, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057206187, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057206801, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057207382, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057207589, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057207658, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057208284, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057208584, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057208750, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057209389, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057209609, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_735FBEC2F1662886.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057209788, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057209962, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057210498, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057211318, "dur": 687, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057212012, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057212502, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057213971, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057214123, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057214713, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057214812, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057216443, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057216589, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057217727, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057217832, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057218435, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057218523, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057218845, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751450057218999, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057219341, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057220364, "dur": 264093, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057488815, "dur": 15423, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751450057488521, "dur": 15797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057504876, "dur": 74850, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751450057504364, "dur": 76518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057582556, "dur": 188, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751450057582995, "dur": 89685, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751450057689650, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751450057689641, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751450057689762, "dur": 86037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057168212, "dur": 26308, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057194523, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057194666, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057195075, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057195898, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450057196113, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751450057196200, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450057196286, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751450057196442, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450057196630, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057196787, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751450057196958, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18074560900205879295.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450057197116, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3951265154693766969.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751450057197235, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057197487, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057198828, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057199527, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057200446, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057200668, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057200869, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057201094, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057201287, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057201487, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057201703, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057201916, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057202122, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057202341, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057202577, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057202784, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057202998, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057203673, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057203886, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057204098, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057204308, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057204525, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057204734, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057204951, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057205151, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057205508, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057205766, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057205943, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057206797, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057207497, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057207740, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057208258, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057208336, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057208417, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057208607, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057209776, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057209848, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057210029, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057210919, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057210988, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057211301, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057212388, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057212470, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057212680, "dur": 1291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057213972, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057214104, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057214847, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057214979, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057215398, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751450057215570, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057216116, "dur": 2748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057218864, "dur": 38289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057257156, "dur": 2079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057259236, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057259431, "dur": 2866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057262298, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057262645, "dur": 3846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057266492, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057266559, "dur": 2195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057268789, "dur": 3171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057271960, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751450057272231, "dur": 2487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751450057274805, "dur": 501009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057168355, "dur": 26171, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057194529, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057194753, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_50CFC6D0BF59D948.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057195035, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057195098, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057195710, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751450057195865, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751450057196115, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751450057196227, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751450057196573, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057197236, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057197668, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057199026, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057199248, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057199477, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057199795, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057200287, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057200577, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057200951, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\ShaderGUI\\GenericShaderGraphMaterialGUI.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751450057200797, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057201556, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057201783, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057202026, "dur": 917, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\FlowCanvas.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751450057202004, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057203116, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057203322, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057203544, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057203787, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057204105, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@026b2a0827a4\\PostProcessing\\Runtime\\Effects\\LensDistortion.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751450057204016, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057204831, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057205048, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057205809, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057205945, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057206803, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057207302, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057207488, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057208156, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057208535, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057208979, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057209185, "dur": 1345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057210531, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057210611, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057211440, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057211557, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057211756, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057212339, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057212482, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057212663, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057213097, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057213283, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057214860, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751450057215037, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057215489, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057215599, "dur": 3253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057218853, "dur": 40430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057259288, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057261851, "dur": 3577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057265429, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057265584, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057267932, "dur": 707, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057268646, "dur": 3568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751450057272214, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057272841, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057272947, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751450057273215, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057273823, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751450057273926, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751450057274057, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057274115, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751450057274443, "dur": 501356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057168675, "dur": 25879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057194555, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450057194778, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D0FAA40E40B36FD7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450057194915, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_847E2D6319A6BEA0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450057194990, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450057195045, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057195860, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751450057195964, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751450057196309, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751450057196618, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057197230, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057197491, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057199232, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057199701, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Operators\\Implementations\\Asin.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751450057199624, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057200468, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057200676, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057200888, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057201146, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057201345, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057201549, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057201758, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057201973, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057202198, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057202402, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057202623, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057202928, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Unity\\AnimationCurveInspector.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751450057202823, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057203661, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057203869, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057204278, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057204485, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057204697, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057204925, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057205131, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057205288, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057205496, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057206126, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057206822, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057207312, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450057207521, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057208198, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057208316, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057209724, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057210081, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057210489, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057210919, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751450057211144, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057211650, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057211801, "dur": 1145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1751450057213017, "dur": 215, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057213439, "dur": 36922, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1751450057257131, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057259262, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057259336, "dur": 2183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057261559, "dur": 2139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057263699, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057263804, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057266025, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057266264, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057268499, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057270722, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057270850, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751450057273255, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751450057273359, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057273618, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057274134, "dur": 149256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751450057423391, "dur": 352399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057168514, "dur": 26028, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057194544, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450057194764, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_A5FFABD953007C60.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450057195028, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450057195741, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751450057195823, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751450057195881, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751450057196045, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751450057196265, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751450057196392, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751450057196607, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057196703, "dur": 494, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751450057197250, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057197492, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057198757, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057198966, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057199338, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057199581, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Operators\\Implementations\\Pi.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751450057199572, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057200438, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057200645, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057200957, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Enumerations\\StructFieldOptions.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751450057200852, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057201730, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057201952, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057202168, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057202375, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057202593, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057202805, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057203013, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057203212, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057203412, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057203679, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057203915, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057204119, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057204326, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057204535, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057204733, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057204933, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057205129, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057205345, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057205534, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057205770, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057205944, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057206823, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057207307, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450057207643, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057207954, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057208941, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450057209113, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057209943, "dur": 690, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057210659, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057212135, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057212398, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057212459, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057212715, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751450057212849, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057214857, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751450057215015, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057215484, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057215962, "dur": 2892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057218855, "dur": 40449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057259306, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057261733, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057261905, "dur": 2611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057264554, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057267260, "dur": 2687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057269948, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057270050, "dur": 2678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751450057272729, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057272930, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057273254, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751450057273456, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057273531, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057273862, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057274126, "dur": 146105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057421700, "dur": 196, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 16, "ts": 1751450057421897, "dur": 1412, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 16, "ts": 1751450057423310, "dur": 71, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 16, "ts": 1751450057420232, "dur": 3153, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751450057423385, "dur": 352389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751450057805946, "dur": 1310, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1380, "tid": 314, "ts": 1751450057829943, "dur": 3382, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1380, "tid": 314, "ts": 1751450057833498, "dur": 2381, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1380, "tid": 314, "ts": 1751450057820198, "dur": 16516, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}