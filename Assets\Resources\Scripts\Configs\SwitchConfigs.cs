using UnityEngine;
using UnityEngine.UI;

public class SwitchConfigs : MonoBehaviour
{
    public GameObject CharButton; // 0
    public GameObject BuffsButton; // 1
    public GameObject PartyButton; // 2

    public GameObject GamePlayConfigs;

    void Start()
    {
        CharButton.GetComponent<Button>().onClick.AddListener(() => SwitchConfs(0));
        BuffsButton.GetComponent<Button>().onClick.AddListener(() => SwitchConfs(1));
        PartyButton.GetComponent<Button>().onClick.AddListener(() => SwitchConfs(2));

        // Disable raycast target on child elements to prevent event propagation issues
        DisableRaycastOnChildElements(CharButton);
        DisableRaycastOnChildElements(BuffsButton);
        DisableRaycastOnChildElements(PartyButton);
    }

    void DisableRaycastOnChildElements(GameObject button)
    {
        // Get all Graphic components (Image, Text, TextMeshPro, etc.) in children
        var childGraphics = button.GetComponentsInChildren<Graphic>();

        foreach (var graphic in childGraphics)
        {
            // Skip the main button's Image component (keep it as raycast target)
            if (graphic.gameObject == button)
                continue;

            // Disable raycast target on child elements
            graphic.raycastTarget = false;
        }
    }

    private void SwitchConfs(int conf) // switch configs
    {
        // checks if the current config is active
        bool isActive = transform.GetChild(conf).GetChild(0).gameObject.activeSelf;
        foreach(var child in transform.GetComponentsInChildren<Button>()) if(child.transform.parent == transform) child.transform.GetChild(0).gameObject.SetActive(false);

        // sets the game play configs acording to the current config
        GamePlayConfigs.SetActive(isActive); 

        // if the config wasn't active, turns it on
        if(!isActive) transform.GetChild(conf).transform.GetChild(0).gameObject.SetActive(true);
    }
}
