using UnityEngine;
using UnityEngine.UI;

public class Add<PERSON>haracter
{
    readonly GameObject valuesPrefab;

    readonly Configs<PERSON>andler configsHandler;

    readonly Transform valuesParentTransform;

    public AddCharacter(int index, ConfigsHandler cH)
    {
        configsHandler = cH;

        // Get the parent transform (this should be the Content of the ScrollRect)
        GameObject contentObject = GameObject.Find("Content");
        if (contentObject == null)
        {
            // Fallback to CharactersInfo if Content is not found
            contentObject = GameObject.Find("CharactersInfo");
            if (contentObject == null)
            {
                Debug.LogError("Neither 'Content' nor 'CharactersInfo' GameObject found! Cannot add character.");
                return;
            }
        }
        valuesParentTransform = contentObject.transform;

        // Load the prefab
        valuesPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");

        // Instantiate the prefab
        GameObject values = Object.Instantiate(valuesPrefab, valuesParentTransform);

        // set the name
        values.name = "Char" + index;

        // set the character
        values.GetComponent<CharConfUI>().character = configsHandler.GetCharacter(index);

        // Refresh the scroll view layout
        RefreshScrollViewLayout();
    }

    void RefreshScrollViewLayout()
    {
        // Find the ScrollViewSetup component and refresh the layout
        ScrollViewSetup scrollSetup = valuesParentTransform.GetComponentInParent<ScrollViewSetup>();
        if (scrollSetup == null)
        {
            // Try to find it in the parent hierarchy
            Transform current = valuesParentTransform.parent;
            while (current != null && scrollSetup == null)
            {
                scrollSetup = current.GetComponent<ScrollViewSetup>();
                current = current.parent;
            }
        }

        if (scrollSetup != null)
        {
            scrollSetup.RefreshLayout();
        }
        else
        {
            // Fallback: Force rebuild layout directly
            LayoutRebuilder.ForceRebuildLayoutImmediate(valuesParentTransform as RectTransform);
        }
    }
}
