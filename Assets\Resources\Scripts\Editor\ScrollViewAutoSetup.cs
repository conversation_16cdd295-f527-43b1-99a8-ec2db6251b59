#if UNITY_EDITOR
using UnityEngine;
using UnityEngine.UI;
using UnityEditor;

[System.Serializable]
public class ScrollViewAutoSetup
{
    [MenuItem("GameObject/UI/Setup Character Scroll View", false, 10)]
    static void SetupCharacterScrollView()
    {
        // Find the CharactersInfo GameObject
        GameObject charactersInfo = GameObject.Find("CharactersInfo");
        if (charactersInfo == null)
        {
            Debug.LogError("CharactersInfo GameObject not found! Please make sure it exists in the scene.");
            return;
        }

        // Check if it already has a ScrollRect
        ScrollRect existingScrollRect = charactersInfo.GetComponentInParent<ScrollRect>();
        if (existingScrollRect != null)
        {
            Debug.Log("ScrollRect already exists in the hierarchy. Setting up components...");
            SetupExistingScrollView(existingScrollRect, charactersInfo);
            return;
        }

        // Create scroll view hierarchy if it doesn't exist
        CreateScrollViewHierarchy(charactersInfo);
    }

    static void SetupExistingScrollView(ScrollRect scrollRect, GameObject charactersInfo)
    {
        // Add ScrollViewSetup component
        ScrollViewSetup setup = scrollRect.GetComponent<ScrollViewSetup>();
        if (setup == null)
        {
            setup = scrollRect.gameObject.AddComponent<ScrollViewSetup>();
        }

        // Configure the setup
        setup.scrollRect = scrollRect;
        setup.content = charactersInfo.GetComponent<RectTransform>();
        
        // Add VerticalLayoutGroup to content
        VerticalLayoutGroup layoutGroup = charactersInfo.GetComponent<VerticalLayoutGroup>();
        if (layoutGroup == null)
        {
            layoutGroup = charactersInfo.AddComponent<VerticalLayoutGroup>();
        }
        setup.layoutGroup = layoutGroup;

        // Add ContentSizeFitter to content
        ContentSizeFitter sizeFitter = charactersInfo.GetComponent<ContentSizeFitter>();
        if (sizeFitter == null)
        {
            sizeFitter = charactersInfo.AddComponent<ContentSizeFitter>();
        }
        setup.contentSizeFitter = sizeFitter;

        // Configure ScrollRect
        scrollRect.content = charactersInfo.GetComponent<RectTransform>();
        scrollRect.horizontal = false;
        scrollRect.vertical = true;

        Debug.Log("Character scroll view setup completed!");
    }

    static void CreateScrollViewHierarchy(GameObject charactersInfo)
    {
        // This method would create a complete scroll view hierarchy
        // For now, we'll just log instructions for manual setup
        Debug.Log("Please manually create a ScrollRect hierarchy with CharactersInfo as the Content, then run this setup again.");
    }
}
#endif
