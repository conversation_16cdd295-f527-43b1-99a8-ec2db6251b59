using UnityEngine;
using UnityEngine.UI;

public class ScrollViewSetup : MonoBehaviour
{
    [Header("Scroll View Components")]
    public ScrollRect scrollRect;
    public RectTransform content;
    public VerticalLayoutGroup layoutGroup;
    public ContentSizeFitter contentSizeFitter;
    
    [Header("Layout Settings")]
    public float spacing = 10f;
    public RectOffset padding = new RectOffset(10, 10, 10, 10);
    
    void Start()
    {
        SetupScrollView();
    }
    
    void SetupScrollView()
    {
        // Find components if not assigned
        if (scrollRect == null)
            scrollRect = GetComponent<ScrollRect>();
            
        if (content == null && scrollRect != null)
            content = scrollRect.content;
            
        if (content != null)
        {
            // Setup Vertical Layout Group
            if (layoutGroup == null)
                layoutGroup = content.GetComponent<VerticalLayoutGroup>();
            if (layoutGroup == null)
                layoutGroup = content.gameObject.AddComponent<VerticalLayoutGroup>();
                
            layoutGroup.spacing = spacing;
            layoutGroup.padding = padding;
            layoutGroup.childAlignment = TextAnchor.UpperCenter;
            layoutGroup.childControlWidth = true;
            layoutGroup.childControlHeight = false;
            layoutGroup.childForceExpandWidth = true;
            layoutGroup.childForceExpandHeight = false;
            
            // Setup Content Size Fitter
            if (contentSizeFitter == null)
                contentSizeFitter = content.GetComponent<ContentSizeFitter>();
            if (contentSizeFitter == null)
                contentSizeFitter = content.gameObject.AddComponent<ContentSizeFitter>();
                
            contentSizeFitter.horizontalFit = ContentSizeFitter.FitMode.Unconstrained;
            contentSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        }
        
        // Setup ScrollRect settings
        if (scrollRect != null)
        {
            scrollRect.horizontal = false;
            scrollRect.vertical = true;
            scrollRect.movementType = ScrollRect.MovementType.Elastic;
            scrollRect.elasticity = 0.1f;
            scrollRect.inertia = true;
            scrollRect.decelerationRate = 0.135f;
            scrollRect.scrollSensitivity = 1.0f;
        }
    }
    
    // Call this method to refresh the layout when items are added/removed
    public void RefreshLayout()
    {
        if (layoutGroup != null)
        {
            LayoutRebuilder.ForceRebuildLayoutImmediate(content);
        }
    }
}
